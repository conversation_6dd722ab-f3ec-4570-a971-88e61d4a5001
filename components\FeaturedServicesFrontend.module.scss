@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.container {
  display: flex;
  justify-content: center;
  width: 100%;
  background-color: $white-four;
  .wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: rem(1300);
    @include only(largeDesktop) {
      width: 100%;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
    @include only(smallDesktop) {
      width: 80%;
      margin-right: rem(80) !important;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
    @include only(tablet) {
      width: 75%;
      margin-right: rem(80) !important;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

    }
    .textContent {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: rem(24);
      max-width: rem(560);
      @include only(largeDesktop) {
        max-width: rem(560);
      }
      @include only(smallDesktop) {
        max-width: rem(450);
      }
      @include only(tablet) {
        align-items: center;
        max-width: rem(360);
      }
      .title {
        font-size: $h2;
        font-weight: 600;
        color: $black-four;
        margin-bottom: rem(16);
        line-height: 1.2;

        @include only(largeDesktop) {
          font-size: $h2;
        }
        @include only(tablet) {
          text-align: center;
        }
      }

      .description {
        font-size: $body;
        line-height: 1.6;
        color: $black-four;
        margin-bottom: rem(16);

        @include only(tablet) {
          text-align: center;
        }
      }

      .features {
        display: flex;
        flex-direction: column;
        gap: rem(16);
        margin-bottom: rem(24);

        .feature {
          display: flex;
          align-items: center;
          gap: rem(32);
          .bullet {
            display: flex;
            justify-content: center;
            align-items: center;
         
          }

          p {
            font-size: $body;
            color: $black-four;
          }
        }
      }

      .buttonContainer {
        margin-top: rem(16);

        .ctaButton {
          @include only(largeDesktop) {
          }
        }
      }
    }

    .imageContainer {
      position: relative;
      flex: 1;
      width: 100%;
      box-shadow: $shadow-six;
      background-color: $white-four;
      display: flex;
      justify-content: center;
      align-items: center;
      height: rem(480);
      max-width: rem(600);
      border-radius: rem(48);

      @include only(largeDesktop) {
        max-width: rem(600);
        height: rem(480);
      }
      @include only(smallDesktop) {
        max-width: rem(600);
        height: rem(480);
      }
      @include only(tablet) {
        max-width: rem(450);
        height: rem(640);
      }
    }
  }
}

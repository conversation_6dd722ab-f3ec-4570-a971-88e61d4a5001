@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.mainSection {
  display: flex;
  justify-content: right;
  align-items: center;
  width: 96vw;
  height: 95vh;
  border-radius: rem(100) rem(150) rem(150) rem(100);
  gap: rem(100);

  .star {
    position: absolute;
    top: 50px;
    left: 50px;
    width: 500;
    height: 500;
  }

  .mainTitle {
    width: rem(640) !important;
    margin-top: rem(64);
    margin-bottom: rem(96);
    font-size: $h1;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    color: $black-four;

    @include only(largeDesktop) {
      width: rem(740);
      margin-top: rem(96);
      font-size: 64px;
      line-height: 1.2;
    }
  }

  .heroSectionSearchContainer {
    margin-right: rem(8);
  }
}

/**
 * ---------------------------------------------------------------------------------
 * This file has been generated by Sanity TypeGen.
 * Command: `sanity typegen generate`
 *
 * Any modifications made directly to this file will be overwritten the next time
 * the TypeScript definitions are generated. Please make changes to the Sanity
 * schema definitions and/or GROQ queries if you need to update these types.
 *
 * For more information on how to use Sanity TypeGen, visit the official documentation:
 * https://www.sanity.io/docs/sanity-typegen
 * ---------------------------------------------------------------------------------
 */

// Source: schema.json
export type MessageReply = {
  _id: string;
  _type: "messageReply";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  parentMessage?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "message";
  };
  sender?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  text?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  timestamp?: string;
};

export type ServiceProvider = {
  _id: string;
  _type: "serviceProvider";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  user?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  name?: string;
  businessName?: string;
  slug?: Slug;
  tagline?: string;
  profileImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    _type: "image";
  };
  gallery?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    _type: "image";
    _key: string;
  }>;
  introVideo?: string;
  description?: string;
  categories?: Array<string>;
  services?: Array<{
    title?: string;
    summary?: string;
    description?: string;
    duration?: string;
    price?: number;
    currency?: string;
    pricingModel?: "fixed" | "hourly" | "per_project";
    notes?: string;
    _key: string;
  }>;
  pricingRange?: "low" | "medium" | "high";
  portfolio?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    caption?: string;
    link?: string;
    _type: "image";
    _key: string;
  } | {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    _type: "file";
    _key: string;
  }>;
  contactInfo?: {
    phone?: string;
    email?: string;
    website?: string;
    social?: Array<{
      platform?: string;
      url?: string;
      _key: string;
    }>;
  };
  location?: {
    address?: string;
    city?: string;
    region?: string;
    country?: string;
    postalCode?: string;
    geo?: Geopoint;
  };
  availability?: Array<{
    day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday";
    from?: string;
    to?: string;
    closed?: boolean;
    _key: string;
  }>;
  booking?: {
    bookingLink?: string;
    bookingProvider?: string;
    acceptsOnline?: boolean;
  };
  languages?: Array<string>;
  responseTime?: string;
  certifications?: Array<string>;
  insurance?: string;
  documents?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    title?: string;
    description?: string;
    _type: "file";
    _key: string;
  }>;
  tags?: Array<string>;
  averageRating?: number;
  reviews?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "review";
  }>;
  policies?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  published?: boolean;
  publishAt?: string;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
  };
  internalNotes?: string;
};

export type RecruitmentAgency = {
  _id: string;
  _type: "recruitmentAgency";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  owner?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  logo?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    _type: "image";
  };
  coverImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    _type: "image";
  };
  description?: string;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    region?: string;
    postalCode?: string;
    country?: string;
    location?: Geopoint;
  };
  contactInfo?: {
    phone?: string;
    email?: string;
    website?: string;
    social?: Array<{
      platform?: string;
      url?: string;
      _type: "socialLink";
      _key: string;
    }>;
  };
  services?: Array<string>;
  specialties?: Array<string>;
  feeStructure?: {
    model?: "contingency" | "retained" | "hourly";
    details?: string;
    typicalPercentage?: string;
  };
  recruiters?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "user";
  }>;
  clients?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "company";
  }>;
  certifications?: Array<string>;
  complianceDocs?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    title?: string;
    description?: string;
    _type: "doc";
    _key: string;
  }>;
  testimonials?: Array<{
    client?: string;
    quote?: string;
    role?: string;
    date?: string;
    _key: string;
  }>;
  intakeFormTemplate?: Array<{
    label?: string;
    key?: string;
    type?: "text" | "textarea" | "select" | "date" | "number" | "checkbox";
    required?: boolean;
    options?: Array<string>;
    _key: string;
  }>;
  officeHours?: Array<{
    day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday";
    from?: string;
    to?: string;
    closed?: boolean;
    _key: string;
  }>;
  averageRating?: number;
  reviews?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "review";
  }>;
  privacy?: "public" | "private";
  published?: boolean;
  publishAt?: string;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
  };
  internalNotes?: string;
};

export type CarDealership = {
  _id: string;
  _type: "carDealership";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  owner?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  logo?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    _type: "image";
  };
  coverImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    _type: "image";
  };
  description?: string;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    region?: string;
    postalCode?: string;
    country?: string;
    location?: Geopoint;
  };
  contactInfo?: {
    phone?: string;
    email?: string;
    website?: string;
    social?: Array<{
      platform?: string;
      url?: string;
      _type: "socialLink";
      _key: string;
    }>;
  };
  openingHours?: Array<{
    day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday";
    open?: string;
    close?: string;
    closed?: boolean;
    _key: string;
  }>;
  inventory?: Array<{
    make?: string;
    model?: string;
    trim?: string;
    year?: number;
    vin?: string;
    mileage?: number;
    condition?: "new" | "used" | "certified";
    price?: number;
    currency?: string;
    images?: Array<{
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      alt?: string;
      _type: "image";
      _key: string;
    }>;
    description?: string;
    features?: Array<string>;
    available?: boolean;
    postedAt?: string;
    _type: "car";
    _key: string;
  }>;
  services?: Array<{
    title?: string;
    description?: string;
    price?: number;
    _key: string;
  }>;
  financing?: {
    offersFinancing?: boolean;
    financingDetails?: string;
    prequalLink?: string;
  };
  averageRating?: number;
  reviews?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "review";
  }>;
  specialties?: Array<string>;
  published?: boolean;
  publishAt?: string;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
  };
  internalNotes?: string;
};

export type ContentChannel = {
  _id: string;
  _type: "contentChannel";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  channelName?: string;
  slug?: Slug;
  owner?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  type?: "blog" | "news" | "influencer" | "podcast" | "video";
  description?: string;
  coverImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    _type: "image";
  };
  followers?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "user";
  }>;
  followersCount?: number;
  socialLinks?: Array<{
    platform?: string;
    url?: string;
    _key: string;
  }>;
  published?: boolean;
};

export type Company = {
  _id: string;
  _type: "company";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  companyName?: string;
  slug?: Slug;
  logo?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    _type: "image";
  };
  industry?: string;
  description?: string;
  employees?: number;
  headquarters?: {
    address?: string;
    city?: string;
    country?: string;
    location?: Geopoint;
  };
  location?: string;
  website?: string;
  social?: Array<{
    platform?: string;
    url?: string;
    _key: string;
  }>;
  recruiter?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  published?: boolean;
};

export type Store = {
  _id: string;
  _type: "store";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  owner?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  logo?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    _type: "image";
  };
  gallery?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    _type: "image";
    _key: string;
  }>;
  description?: string;
  category?: Array<string>;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    region?: string;
    postalCode?: string;
    country?: string;
    location?: Geopoint;
  };
  contactInfo?: {
    phone?: string;
    email?: string;
    website?: string;
    social?: Array<{
      platform?: string;
      url?: string;
      _type: "link";
      _key: string;
    }>;
  };
  openingHours?: Array<{
    day?: "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday" | "Saturday" | "Sunday";
    open?: string;
    close?: string;
    closed?: boolean;
    _key: string;
  }>;
  averageRating?: number;
  reviews?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "review";
  }>;
  published?: boolean;
  publishAt?: string;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
  };
  internalNotes?: string;
};

export type ServicesListing = {
  _id: string;
  _type: "servicesListing";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  category?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "servicesCategory";
  };
  serviceType?: "consulting" | "web-development" | "graphic-design" | "photography" | "home-repair" | "cleaning" | "tutoring" | "marketing" | "fitness-training" | "pet-care" | "legal-services" | "financial-services" | "other";
  providerName?: string;
  providerWebsite?: string;
  serviceArea?: Array<string>;
  availability?: "immediate" | "within-24-hours" | "within-3-5-days" | "custom-schedule";
  languages?: Array<string>;
  testimonials?: Array<{
    quote?: string;
    author?: string;
    authorTitle?: string;
    _key: string;
  }>;
  bookingLink?: string;
  portfolioUrl?: string;
  certifications?: Array<string>;
  averageRating?: number;
  reviewsCount?: number;
  keywords?: Array<string>;
  details?: Array<{
    condition?: string;
    accessoriesIncluded?: Array<string>;
    warrantyInformation?: string;
    history?: string;
    customizations?: string;
    maintenanceHistory?: string;
    compatibility?: string;
    originalPackaging?: string;
    usageHistory?: string;
    storage?: string;
    originalPurchaseDate?: string;
    reasonForSelling?: string;
    additionalFeatures?: string;
    serviceRecords?: string;
    userManualAvailability?: string;
    manufacturerSupport?: string;
    compatibilityWithAccessories?: string;
    packagingCondition?: string;
    productHistory?: string;
    transferability?: string;
    petSmokeExposure?: string;
    regulatoryCompliance?: string;
    specialFeatures?: string;
    documentation?: string;
    certification?: string;
    age?: string;
    ownership?: string;
    environmentalImpact?: string;
    knownIssues?: string;
    upgrades?: string;
    _type: "details";
    _key: string;
  }>;
  user?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  site?: "oly" | "oly-properties" | "oly-auto" | "oly-hiring" | "oly-services";
  isActive?: boolean;
  approvedForListing?: "approved" | "pending" | "denied";
  featured?: boolean;
  price?: number;
  currency?: "ZAR" | "USD" | "EUR" | "GBP";
  pricingOption?: "fixed_price" | "hourly_rate" | "by_project" | "contact_for_quote" | "negotiable";
  priceId?: string;
  paystackId?: string;
  avatar?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "imageFile";
  };
  images?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "imageFile";
  }>;
  featuredImage?: string;
  videos?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "videoFile";
  }>;
  attachments?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "attachment";
  }>;
  location?: Location;
  promotions?: Array<{
    platform?: "oly" | "facebook-ads" | "google-ads" | "instagram-ads" | "thumbtack" | "yelp";
    duration?: "7days" | "2weeks" | "1month" | "3months";
    remainingDays?: number;
    _key: string;
  }>;
  likes?: number;
  todaysViews?: number;
  totalViews?: number;
  unreadMessages?: number;
  condition?: string;
  quantity?: number;
};

export type ServicesCategory = {
  _id: string;
  _type: "servicesCategory";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  path?: string;
  parent?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "servicesCategory";
  };
  subcategories?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "servicesCategory";
  }>;
  level?: number;
  isRoot?: boolean;
  description?: string;
  icon?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  isActive?: boolean;
  isFeatured?: boolean;
  order?: number;
  serviceTypes?: Array<string>;
  pricingModels?: Array<string>;
  hourlyRateRange?: {
    min?: number;
    max?: number;
    currency?: "USD" | "EUR" | "LOCAL";
  };
  packages?: Array<{
    title?: string;
    price?: number;
    description?: string;
    duration?: number;
    _key: string;
  }>;
  bookingRequired?: boolean;
  durationOptions?: Array<string>;
  skills?: Array<string>;
  certifications?: Array<string>;
  areasServed?: Array<string>;
  responseTime?: string;
  filters?: {
    price?: boolean;
    rating?: boolean;
    availability?: boolean;
    distance?: boolean;
    certification?: boolean;
    remote?: boolean;
  };
  listingHints?: string;
  minPrice?: number;
  maxPrice?: number;
  currency?: "USD" | "EUR" | "LOCAL";
  itemCount?: number;
  lastIndexedAt?: string;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: Array<string>;
  };
};

export type JobListing = {
  _id: string;
  _type: "jobListing";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  category?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "jobCategory";
  };
  company?: string;
  companyWebsite?: string;
  employmentType?: "full-time" | "part-time" | "contract" | "temporary" | "internship";
  experienceLevel?: "entry-level" | "associate" | "mid-senior" | "director" | "executive";
  educationLevel?: "high-school" | "associate-degree" | "bachelors-degree" | "masters-degree" | "doctorate" | "vocational" | "not-specified";
  remoteOption?: "on-site" | "remote" | "hybrid";
  applicationUrl?: string;
  benefits?: Array<string>;
  seniorityLevel?: "internship" | "entry-level" | "junior" | "mid-level" | "senior" | "lead-principal" | "manager" | "director" | "executive";
  hiringTeam?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "user";
  }>;
  openPositions?: number;
  requirements?: Array<string>;
  responsibilities?: Array<string>;
  details?: Array<{
    condition?: string;
    accessoriesIncluded?: Array<string>;
    warrantyInformation?: string;
    history?: string;
    customizations?: string;
    maintenanceHistory?: string;
    compatibility?: string;
    originalPackaging?: string;
    usageHistory?: string;
    storage?: string;
    originalPurchaseDate?: string;
    reasonForSelling?: string;
    additionalFeatures?: string;
    serviceRecords?: string;
    userManualAvailability?: string;
    manufacturerSupport?: string;
    compatibilityWithAccessories?: string;
    packagingCondition?: string;
    productHistory?: string;
    transferability?: string;
    petSmokeExposure?: string;
    regulatoryCompliance?: string;
    specialFeatures?: string;
    documentation?: string;
    certification?: string;
    age?: string;
    ownership?: string;
    environmentalImpact?: string;
    knownIssues?: string;
    upgrades?: string;
    _type: "details";
    _key: string;
  }>;
  user?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  site?: "oly" | "oly-properties" | "oly-auto" | "oly-hiring" | "oly-services";
  isActive?: boolean;
  approvedForListing?: "approved" | "pending" | "denied";
  featured?: boolean;
  salary?: {
    min?: number;
    max?: number;
    frequency?: "hourly" | "daily" | "weekly" | "bi-weekly" | "monthly" | "annually";
    negotiable?: boolean;
  };
  currency?: "ZAR" | "USD" | "EUR" | "GBP";
  pricingOption?: "standard" | "premium" | "free";
  priceId?: string;
  paystackId?: string;
  avatar?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "imageFile";
  };
  images?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "imageFile";
  }>;
  featuredImage?: string;
  videos?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "videoFile";
  }>;
  attachments?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "attachment";
  }>;
  location?: Location;
  promotions?: Array<{
    platform?: "oly" | "facebook" | "google-ads" | "linkedin-ads" | "indeed" | "glassdoor";
    duration?: "7days" | "2weeks" | "1month" | "2months";
    remainingDays?: number;
    _key: string;
  }>;
  likes?: number;
  todaysViews?: number;
  totalViews?: number;
  unreadMessages?: number;
  postedOn?: string;
  expiresAt?: string;
  condition?: string;
  quantity?: number;
};

export type JobCategory = {
  _id: string;
  _type: "jobCategory";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  path?: string;
  parent?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "jobCategory";
  };
  subcategories?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "jobCategory";
  }>;
  level?: number;
  isRoot?: boolean;
  description?: string;
  icon?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  isActive?: boolean;
  isFeatured?: boolean;
  order?: number;
  jobTypes?: Array<string>;
  seniorityLevels?: Array<string>;
  industries?: Array<string>;
  employmentType?: Array<string>;
  remoteOptions?: Array<string>;
  salaryRange?: {
    minSalary?: number;
    maxSalary?: number;
    currency?: "USD" | "EUR" | "LOCAL";
  };
  experienceYears?: {
    minYears?: number;
    maxYears?: number;
  };
  qualifications?: Array<string>;
  skills?: Array<string>;
  benefits?: Array<string>;
  filters?: {
    salary?: boolean;
    jobType?: boolean;
    seniority?: boolean;
    remote?: boolean;
    experience?: boolean;
    skills?: boolean;
    industry?: boolean;
  };
  listingHints?: string;
  minSalaryAllowed?: number;
  maxSalaryAllowed?: number;
  itemCount?: number;
  lastIndexedAt?: string;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: Array<string>;
  };
};

export type AutoListing = {
  _id: string;
  _type: "autoListing";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  category?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "vehicleCategory";
  };
  vehicleType?: "car" | "motorcycle" | "truck" | "van" | "suv" | "boat" | "rv-camper" | "heavy-equipment" | "other";
  listingType?: "for-sale" | "for-rent" | "lease-transfer" | "auction";
  make?: string;
  model?: string;
  year?: number;
  mileage?: {
    value?: number;
    unit?: "miles" | "km";
  };
  fuelType?: "petrol" | "diesel" | "electric" | "hybrid" | "lpg" | "other";
  transmission?: "automatic" | "manual" | "semi-automatic";
  engineSize?: string;
  colorExterior?: string;
  colorInterior?: string;
  doors?: 2 | 3 | 4 | 5 | 6;
  vin?: string;
  driveType?: "FWD" | "RWD" | "AWD" | "4WD";
  bodyStyle?: "sedan" | "hatchback" | "coupe" | "convertible" | "suv" | "truck" | "minivan" | "wagon" | "sports-car" | "electric" | "other";
  seatingCapacity?: number;
  serviceHistoryAvailable?: boolean;
  warranty?: "manufacturer-warranty" | "dealer-warranty" | "aftermarket-warranty" | "no-warranty";
  carfaxReportUrl?: string;
  user?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  site?: "oly" | "oly-properties" | "oly-auto" | "oly-hiring" | "oly-services";
  isActive?: boolean;
  approvedForListing?: "approved" | "pending" | "denied";
  featured?: boolean;
  price?: number;
  currency?: "ZAR" | "USD" | "EUR" | "GBP";
  pricingOption?: "negotiable" | "fixed_price" | "contact_for_price";
  priceId?: string;
  paystackId?: string;
  images?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "imageFile";
  }>;
  featuredImage?: string;
  videos?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "videoFile";
  }>;
  attachments?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "attachment";
  }>;
  location?: Location;
  condition?: "new" | "used-excellent" | "used-good" | "used-fair" | "salvage";
  quantity?: number;
  details?: Array<{
    condition?: string;
    accessoriesIncluded?: Array<string>;
    warrantyInformation?: string;
    history?: string;
    customizations?: string;
    maintenanceHistory?: string;
    compatibility?: string;
    originalPackaging?: string;
    usageHistory?: string;
    storage?: string;
    originalPurchaseDate?: string;
    reasonForSelling?: string;
    additionalFeatures?: string;
    serviceRecords?: string;
    userManualAvailability?: string;
    manufacturerSupport?: string;
    compatibilityWithAccessories?: string;
    packagingCondition?: string;
    productHistory?: string;
    transferability?: string;
    petSmokeExposure?: string;
    regulatoryCompliance?: string;
    specialFeatures?: string;
    documentation?: string;
    certification?: string;
    age?: string;
    ownership?: string;
    environmentalImpact?: string;
    knownIssues?: string;
    upgrades?: string;
    _type: "details";
    _key: string;
  }>;
  features?: Array<string>;
  avatar?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  promotions?: Array<{
    platform?: "oly" | "facebook" | "google-ads" | "instagram" | "linkedin" | "auto-trader" | "cars-com";
    duration?: "1day" | "7days" | "2weeks" | "1month" | "3months";
    remainingDays?: number;
    _key: string;
  }>;
  likes?: number;
  todaysViews?: number;
  totalViews?: number;
  unreadMessages?: number;
  postedOn?: string;
  expiresAt?: string;
};

export type VehicleCategory = {
  _id: string;
  _type: "vehicleCategory";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  path?: string;
  parent?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "vehicleCategory";
  };
  subcategories?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "vehicleCategory";
  }>;
  level?: number;
  isRoot?: boolean;
  description?: string;
  icon?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  isActive?: boolean;
  isFeatured?: boolean;
  order?: number;
  listingTypes?: Array<string>;
  vehicleTypes?: Array<string>;
  bodyTypes?: Array<string>;
  fuelTypes?: Array<string>;
  transmissions?: Array<string>;
  engineCapacityOptions?: Array<string>;
  yearRange?: {
    minYear?: number;
    maxYear?: number;
  };
  mileage?: {
    enabled?: boolean;
    buckets?: Array<string>;
  };
  seatingCapacity?: Array<number>;
  drivetrain?: Array<string>;
  colorOptions?: Array<string>;
  emissions?: {
    co2Enabled?: boolean;
    euroStandard?: boolean;
  };
  features?: Array<string>;
  filters?: {
    price?: boolean;
    makeModel?: boolean;
    year?: boolean;
    mileage?: boolean;
    fuelType?: boolean;
    transmission?: boolean;
    seats?: boolean;
    features?: boolean;
  };
  listingHints?: string;
  minPrice?: number;
  maxPrice?: number;
  currency?: "USD" | "EUR" | "LOCAL";
  itemCount?: number;
  lastIndexedAt?: string;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: Array<string>;
  };
};

export type PropertyListing = {
  _id: string;
  _type: "propertyListing";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  category?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "propertyCategory";
  };
  propertyType?: "house" | "apartment" | "townhouse" | "condo" | "land" | "commercial" | "office-space" | "retail-space" | "warehouse" | "farm" | "other";
  listingType?: "for-sale" | "for-rent" | "auction";
  bedrooms?: number;
  bathrooms?: number;
  garages?: number;
  areaSize?: {
    value?: number;
    unit?: "sqft" | "sqm" | "acres" | "hectares";
  };
  landSize?: {
    value?: number;
    unit?: "sqft" | "sqm" | "acres" | "hectares";
  };
  buildYear?: number;
  lotNumber?: string;
  buildingName?: string;
  floorNumber?: number;
  amenities?: Array<string>;
  hoaFees?: number;
  propertyTaxes?: number;
  zoning?: string;
  energyRating?: "A+" | "A" | "B" | "C" | "D" | "E" | "F" | "G" | "Not Applicable";
  user?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  site?: "oly" | "oly-properties" | "oly-auto" | "oly-hiring" | "oly-services";
  isActive?: boolean;
  approvedForSale?: "approved" | "pending" | "denied";
  featured?: boolean;
  price?: number;
  currency?: "ZAR" | "USD" | "EUR" | "GBP";
  pricingOption?: "negotiable" | "fixed_price" | "contact_for_price" | "POA";
  priceId?: string;
  paystackId?: string;
  images?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "imageFile";
  }>;
  featuredImage?: string;
  videos?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "videoFile";
  }>;
  floorPlans?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
    _key: string;
  }>;
  virtualTourUrl?: string;
  attachments?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "attachment";
  }>;
  location?: Location;
  condition?: "new-construction" | "recently-renovated" | "excellent" | "good" | "needs-renovation";
  quantity?: number;
  details?: Array<{
    condition?: string;
    accessoriesIncluded?: Array<string>;
    warrantyInformation?: string;
    history?: string;
    customizations?: string;
    maintenanceHistory?: string;
    compatibility?: string;
    originalPackaging?: string;
    usageHistory?: string;
    storage?: string;
    originalPurchaseDate?: string;
    reasonForSelling?: string;
    additionalFeatures?: string;
    serviceRecords?: string;
    userManualAvailability?: string;
    manufacturerSupport?: string;
    compatibilityWithAccessories?: string;
    packagingCondition?: string;
    productHistory?: string;
    transferability?: string;
    petSmokeExposure?: string;
    regulatoryCompliance?: string;
    specialFeatures?: string;
    documentation?: string;
    certification?: string;
    age?: string;
    ownership?: string;
    environmentalImpact?: string;
    knownIssues?: string;
    upgrades?: string;
    _type: "details";
    _key: string;
  }>;
  features?: Array<string>;
  avatar?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  promotions?: Array<{
    platform?: "oly" | "facebook" | "google-ads" | "instagram" | "linkedin" | "property-portal-x";
    duration?: "1day" | "7days" | "2weeks" | "1month" | "3months";
    remainingDays?: number;
    _key: string;
  }>;
  likes?: number;
  todaysViews?: number;
  totalViews?: number;
  unreadMessages?: number;
  postedOn?: string;
  expiresAt?: string;
};

export type PropertyCategory = {
  _id: string;
  _type: "propertyCategory";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  path?: string;
  parent?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "propertyCategory";
  };
  subcategories?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "propertyCategory";
  }>;
  level?: number;
  isRoot?: boolean;
  description?: string;
  icon?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  isActive?: boolean;
  isFeatured?: boolean;
  order?: number;
  listingTypes?: Array<string>;
  propertyTypes?: Array<string>;
  defaultFields?: Array<{
    name?: string;
    label?: string;
    type?: "text" | "number" | "boolean" | "select" | "date";
    required?: boolean;
    options?: Array<string>;
    _key: string;
  }>;
  filters?: {
    price?: boolean;
    bedrooms?: boolean;
    bathrooms?: boolean;
    area?: boolean;
    furnished?: boolean;
    parking?: boolean;
    amenities?: boolean;
  };
  listingHints?: string;
  amenities?: Array<string>;
  minPrice?: number;
  maxPrice?: number;
  currency?: "USD" | "EUR" | "LOCAL";
  availabilityOptions?: Array<string>;
  itemCount?: number;
  lastIndexedAt?: string;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: Array<string>;
  };
};

export type ArticleCard = {
  _type: "articleCard";
  articleReference?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "articlePage";
  };
  showAuthorAvatar?: boolean;
  showAuthorName?: boolean;
  showCategoryTag?: boolean;
  cardLayout?: "default" | "compact" | "minimal";
};

export type OlyHomepage = {
  _id: string;
  _type: "olyHomepage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  isActive?: boolean;
  heroSection?: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "heroSection";
    };
    sortOrder?: number;
  };
  moreFromOlySection?: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "moreFromOlySection";
    };
    sortOrder?: number;
  };
  featuredServicesSection?: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "featuredServicesSection";
    };
    sortOrder?: number;
  };
  featuredCategoriesSection?: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "featuredCategoriesSection";
    };
    sortOrder?: number;
  };
  featuredListingsSection?: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "featuredListingsSection";
    };
    sortOrder?: number;
  };
  adSection?: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "adSection";
    };
    sortOrder?: number;
  };
  topAdSection?: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "topAdSection";
    };
    sortOrder?: number;
  };
  bottomAdSection?: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "bottomAdSection";
    };
    sortOrder?: number;
  };
  olyArticlesSection?: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "olyArticlesSection";
    };
    sortOrder?: number;
  };
  sponsoredArticlesSection?: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sponsoredArticlesSection";
    };
    sortOrder?: number;
  };
  seoSettings?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: Array<string>;
  };
  publishedAt?: string;
};

export type SponsoredArticlesSection = {
  _id: string;
  _type: "sponsoredArticlesSection";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  isActive?: boolean;
  displayOrder?: number;
  subtitle?: string;
  articleSelection?: {
    selectionMethod?: "manual" | "auto-active" | "auto-highest-paying" | "auto-recent" | "auto-performing" | "priority-premium" | "mixed";
    manualSponsoredArticles?: Array<{
      sponsoredArticleReference?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sponsoredArticle";
      };
      sponsorshipDetails?: {
        sponsor?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sponsor";
        };
        sponsorshipTier?: "basic" | "premium" | "vip" | "enterprise";
        sponsorshipStartDate?: string;
        sponsorshipEndDate?: string;
        sponsorshipAmount?: number;
      };
      customTitle?: string;
      customExcerpt?: string;
      customFeaturedImage?: {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        alt?: string;
        _type: "image";
      };
      priority?: number;
      isActive?: boolean;
      _type: "featuredSponsoredArticle";
      _key: string;
    }>;
    autoSelectionCriteria?: {
      maxArticles?: number;
      sponsorFilter?: Array<{
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        _key: string;
        [internalGroqTypeReferenceTo]?: "sponsor";
      }>;
      sponsorshipTierFilter?: Array<"basic" | "premium" | "vip" | "enterprise">;
      categoryFilter?: Array<{
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        _key: string;
        [internalGroqTypeReferenceTo]?: "articleCategory";
      }>;
      minSponsorshipAmount?: number;
      activeSponsorsOnly?: boolean;
      requireFeaturedImage?: boolean;
      approvedOnly?: boolean;
      maxAge?: number;
    };
  };
  sponsorshipDisclosure?: {
    showSponsoredBadge?: boolean;
    sponsoredBadgeText?: string;
    badgeStyle?: "gold" | "blue" | "green" | "orange" | "gray";
    showSponsorName?: boolean;
    sponsorNameFormat?: "sponsored-by" | "partnership" | "presents" | "name-only";
    disclosureText?: string;
  };
  carouselConfiguration?: {
    autoplay?: boolean;
    autoplaySpeed?: number;
    showNavigation?: boolean;
    showDots?: boolean;
    articlesPerView?: {
      desktop?: 3 | 4 | 5 | 6;
      tablet?: 2 | 3 | 4;
      mobile?: 1 | 2;
    };
    scrollBehavior?: "single" | "page" | "free";
    pauseOnHover?: boolean;
  };
  cardConfiguration?: {
    cardAspectRatio?: "1:1" | "3:4" | "4:3" | "2:3";
    imageAspectRatio?: "1:1" | "16:9" | "4:3" | "3:4";
    showExcerpt?: boolean;
    excerptLength?: number;
    showAuthor?: boolean;
    showPublishDate?: boolean;
    showReadTime?: boolean;
    showCategory?: boolean;
    hoverEffect?: "none" | "scale" | "lift" | "image-zoom" | "overlay" | "glow";
    cardStyle?: {
      backgroundColor?: "white" | "light-gray" | "light-gold" | "transparent";
      borderRadius?: "none" | "small" | "medium" | "large";
      shadow?: "none" | "small" | "medium" | "large" | "gold-glow";
      border?: "none" | "light" | "medium" | "gold-accent";
    };
  };
  sectionStyle?: {
    backgroundColor?: "white" | "light-gray" | "off-white" | "light-gold" | "custom";
    customBackgroundColor?: string;
    padding?: "small" | "medium" | "large";
    titleAlignment?: "left" | "center" | "right";
    cardSpacing?: "tight" | "normal" | "loose";
  };
  sponsorAnalytics?: {
    trackSponsoredViews?: boolean;
    trackSponsoredClicks?: boolean;
    trackSponsorClicks?: boolean;
    trackCarouselEngagement?: boolean;
    generateSponsorReports?: boolean;
  };
  publishedAt?: string;
};

export type Sponsor = {
  _id: string;
  _type: "sponsor";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  sponsorTemp?: string;
};

export type SponsoredArticle = {
  _id: string;
  _type: "sponsoredArticle";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  sponsoredArticleTEMP?: string;
};

export type OlyArticlesSection = {
  _id: string;
  _type: "olyArticlesSection";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  isActive?: boolean;
  displayOrder?: number;
  subtitle?: string;
  articleSelection?: {
    selectionMethod?: "manual" | "auto-recent" | "auto-popular" | "auto-featured" | "auto-category" | "mixed";
    manualArticles?: Array<{
      articleReference?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "articlePage";
      };
      customTitle?: string;
      customExcerpt?: string;
      customFeaturedImage?: {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        alt?: string;
        _type: "image";
      };
      priority?: number;
      isActive?: boolean;
      _type: "featuredArticle";
      _key: string;
    }>;
    autoSelectionCriteria?: {
      maxArticles?: number;
      authorFilter?: Array<{
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        _key: string;
        [internalGroqTypeReferenceTo]?: "author";
      }>;
      categoryFilter?: Array<{
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        _key: string;
        [internalGroqTypeReferenceTo]?: "articleCategory";
      }>;
      excludeCategories?: Array<{
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        _key: string;
        [internalGroqTypeReferenceTo]?: "articleCategory";
      }>;
      maxAge?: number;
      requireFeaturedImage?: boolean;
      minReadTime?: number;
      staffOnly?: boolean;
    };
  };
  carouselConfiguration?: {
    autoplay?: boolean;
    autoplaySpeed?: number;
    showNavigation?: boolean;
    showDots?: boolean;
    articlesPerView?: {
      desktop?: 3 | 4 | 5 | 6;
      tablet?: 2 | 3 | 4;
      mobile?: 1 | 2;
    };
    scrollBehavior?: "single" | "page" | "free";
    pauseOnHover?: boolean;
  };
  cardConfiguration?: {
    cardAspectRatio?: "1:1" | "3:4" | "4:3" | "2:3";
    imageAspectRatio?: "1:1" | "16:9" | "4:3" | "3:4";
    showExcerpt?: boolean;
    excerptLength?: number;
    showAuthor?: boolean;
    showPublishDate?: boolean;
    showReadTime?: boolean;
    showCategory?: boolean;
    hoverEffect?: "none" | "scale" | "lift" | "image-zoom" | "overlay";
    cardStyle?: {
      backgroundColor?: "white" | "light-gray" | "transparent";
      borderRadius?: "none" | "small" | "medium" | "large";
      shadow?: "none" | "small" | "medium" | "large";
      border?: "none" | "light" | "medium";
    };
  };
  sectionStyle?: {
    backgroundColor?: "white" | "light-gray" | "off-white" | "custom";
    customBackgroundColor?: string;
    padding?: "small" | "medium" | "large";
    titleAlignment?: "left" | "center" | "right";
    cardSpacing?: "tight" | "normal" | "loose";
  };
  callToAction?: {
    showMoreArticlesButton?: boolean;
    moreArticlesButtonText?: string;
    moreArticlesButtonUrl?: string;
    showForumButton?: boolean;
    forumButtonText?: string;
    forumButtonUrl?: string;
    buttonStyle?: "light" | "primary" | "secondary" | "outline";
    buttonAlignment?: "left" | "center" | "right";
  };
  analytics?: {
    trackArticleViews?: boolean;
    trackArticleClicks?: boolean;
    trackAuthorClicks?: boolean;
    trackCarouselNavigation?: boolean;
  };
  publishedAt?: string;
};

export type ArticlePage = {
  _id: string;
  _type: "articlePage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  excerpt?: string;
  heroSection?: {
    heroImage?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      alt?: string;
      caption?: string;
      _type: "image";
    };
    heroOverlay?: {
      showOverlay?: boolean;
      overlayText?: string;
      overlayPosition?: "top-left" | "top-center" | "top-right" | "center-left" | "center" | "center-right" | "bottom-left" | "bottom-center" | "bottom-right";
    };
  };
  articleMeta?: {
    author?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "author";
    };
    publishedAt?: string;
    updatedAt?: string;
    readingTime?: number;
    category?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "articleCategory";
    };
    tags?: Array<string>;
  };
  content?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      blank?: boolean;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  } | {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    caption?: string;
    imageSize?: "small" | "medium" | "large" | "full";
    _type: "image";
    _key: string;
  } | {
    language?: "javascript" | "typescript" | "html" | "css" | "python" | "json" | "bash" | "markdown" | "sql" | "php" | "ruby" | "java" | "csharp" | "go";
    code?: string;
    filename?: string;
    highlightedLines?: Array<number>;
    _type: "codeBlock";
    _key: string;
  } | {
    type?: "info" | "warning" | "success" | "error" | "note";
    title?: string;
    content?: Array<{
      children?: Array<{
        marks?: Array<string>;
        text?: string;
        _type: "span";
        _key: string;
      }>;
      style?: "normal";
      listItem?: never;
      markDefs?: Array<{
        href?: string;
        _type: "link";
        _key: string;
      }>;
      level?: number;
      _type: "block";
      _key: string;
    }>;
    icon?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _type: "calloutBox";
    _key: string;
  }>;
  sidebarConfig?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "articleSidebar";
  };
  commentsConfig?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "commentsSection";
  };
  seoSettings?: {
    metaTitle?: string;
    metaDescription?: string;
    focusKeyword?: string;
    canonicalUrl?: string;
    noIndex?: boolean;
  };
  socialSharing?: {
    enableSharing?: boolean;
    shareImage?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      alt?: string;
      _type: "image";
    };
    shareTitle?: string;
    shareDescription?: string;
  };
  publishStatus?: "draft" | "published" | "scheduled" | "archived";
  scheduledPublishAt?: string;
};

export type CommentsSection = {
  _id: string;
  _type: "commentsSection";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  enableComments?: boolean;
  commentSystem?: "builtin" | "disqus" | "facebook" | "custom";
  moderateComments?: boolean;
  allowGuestComments?: boolean;
  disqusShortname?: string;
  facebookAppId?: string;
  customSystemEndpoint?: string;
};

export type ArticleSidebar = {
  _id: string;
  _type: "articleSidebar";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  showSidebar?: boolean;
  relatedArticles?: {
    showRelatedArticles?: boolean;
    relatedArticlesTitle?: string;
    maxRelatedArticles?: number;
    relatedBy?: "category" | "tags" | "author" | "manual";
    manualRelatedArticles?: Array<{
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      _key: string;
      [internalGroqTypeReferenceTo]?: "articlePage";
    }>;
  };
  sidebarAds?: {
    showAds?: boolean;
    adSectionReference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "adSection";
    };
    adPositions?: Array<"top" | "middle" | "bottom" | "between-articles">;
  };
};

export type ArticleCategory = {
  _id: string;
  _type: "articleCategory";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: string;
  parentCategory?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "articleCategory";
  };
  icon?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
};

export type Author = {
  _id: string;
  _type: "author";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    _type: "image";
  };
  bio?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal";
    listItem?: never;
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  socialLinks?: Array<{
    platform?: "twitter" | "linkedin" | "github" | "website" | "other";
    url?: string;
    _type: "socialLink";
    _key: string;
  }>;
};

export type BottomAdSection = {
  _id: string;
  _type: "bottomAdSection";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  isActive?: boolean;
  sectionTitle?: string;
  adSectionReference?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "adSection";
  };
  positionSettings?: {
    placement?: "above-footer" | "below-content" | "bottom-page" | "before-related" | "after-related";
    isSticky?: boolean;
    stickyOffset?: number;
    zIndex?: number;
    showOnScrollUp?: boolean;
  };
  displaySettings?: {
    showOnPages?: Array<"homepage" | "properties-homepage" | "auto-homepage" | "hiring-homepage" | "services-homepage" | "listing-pages" | "search-results" | "article-pages" | "all-pages">;
    showAfterScrollPercentage?: number;
    showAfterTimeOnPage?: number;
    hideAfterInteraction?: boolean;
    showCloseButton?: boolean;
    showMinimizeButton?: boolean;
    autoHideDelay?: number;
    showOnlyOnce?: boolean;
    respectDoNotTrack?: boolean;
  };
  bottomAdStyle?: {
    width?: "full" | "container" | "custom";
    customWidth?: string;
    alignment?: "left" | "center" | "right";
    backgroundColor?: "transparent" | "white" | "light-gray" | "dark-gray" | "brand" | "custom";
    customBackgroundColor?: string;
    borderStyle?: {
      showBorder?: boolean;
      borderColor?: "light-gray" | "dark-gray" | "brand" | "custom";
      customBorderColor?: string;
      borderWidth?: "thin" | "medium" | "thick";
      borderPosition?: "all" | "top" | "top-bottom";
    };
    shadow?: "none" | "small" | "medium" | "large" | "top-only";
    padding?: "none" | "small" | "medium" | "large";
    margin?: "none" | "small" | "medium" | "large";
    animationStyle?: "none" | "slide-up" | "fade-in" | "scale-in" | "bounce-in";
  };
  responsiveSettings?: {
    hideOnMobile?: boolean;
    hideOnTablet?: boolean;
    mobileHeight?: "auto" | "small" | "medium" | "large";
    disableStickyOnMobile?: boolean;
    mobileOnlySticky?: boolean;
    tabletBehavior?: "desktop" | "mobile" | "custom";
  };
  performanceSettings?: {
    lazyLoad?: boolean;
    preloadAds?: boolean;
    cacheAds?: boolean;
    loadPriority?: "high" | "normal" | "low";
    intersectionThreshold?: number;
  };
  userExperienceSettings?: {
    respectReducedMotion?: boolean;
    keyboardAccessible?: boolean;
    screenReaderFriendly?: boolean;
    respectBatteryLevel?: boolean;
  };
  analytics?: {
    trackVisibility?: boolean;
    trackScrollToAd?: boolean;
    trackTimeInView?: boolean;
    trackCloseEvents?: boolean;
    trackMinimizeEvents?: boolean;
    customAnalyticsCode?: string;
  };
  publishedAt?: string;
};

export type TopAdSection = {
  _id: string;
  _type: "topAdSection";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  isActive?: boolean;
  sectionTitle?: string;
  adSectionReference?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "adSection";
  };
  positionSettings?: {
    placement?: "above-header" | "below-header" | "above-hero" | "below-hero" | "top-content";
    isSticky?: boolean;
    stickyOffset?: number;
    zIndex?: number;
  };
  displaySettings?: {
    showOnPages?: Array<"homepage" | "properties-homepage" | "auto-homepage" | "hiring-homepage" | "services-homepage" | "listing-pages" | "search-results" | "all-pages">;
    hideAfterInteraction?: boolean;
    showCloseButton?: boolean;
    autoHideDelay?: number;
    showOnlyOnce?: boolean;
  };
  topAdStyle?: {
    width?: "full" | "container" | "custom";
    customWidth?: string;
    alignment?: "left" | "center" | "right";
    backgroundColor?: "transparent" | "white" | "light-gray" | "dark-gray" | "brand" | "custom";
    customBackgroundColor?: string;
    borderStyle?: {
      showBorder?: boolean;
      borderColor?: "light-gray" | "dark-gray" | "brand" | "custom";
      customBorderColor?: string;
      borderWidth?: "thin" | "medium" | "thick";
    };
    shadow?: "none" | "small" | "medium" | "large";
    padding?: "none" | "small" | "medium" | "large";
    margin?: "none" | "small" | "medium" | "large";
  };
  responsiveSettings?: {
    hideOnMobile?: boolean;
    hideOnTablet?: boolean;
    mobileHeight?: "auto" | "small" | "medium" | "large";
    disableStickyOnMobile?: boolean;
  };
  performanceSettings?: {
    lazyLoad?: boolean;
    preloadAds?: boolean;
    cacheAds?: boolean;
  };
  analytics?: {
    trackVisibility?: boolean;
    trackScrollDepth?: boolean;
    trackCloseEvents?: boolean;
    customAnalyticsCode?: string;
  };
  publishedAt?: string;
};

export type AdSection = {
  _id: string;
  _type: "adSection";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  isActive?: boolean;
  sectionTitle?: string;
  adType?: "sponsored" | "network" | "mixed";
  sponsoredAds?: Array<{
    adTitle?: string;
    businessName?: string;
    adDescription?: string;
    adImage?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      alt?: string;
      _type: "image";
    };
    adUrl?: string;
    callToAction?: "learn-more" | "shop-now" | "get-quote" | "contact-us" | "visit-website" | "book-now" | "custom";
    customCTA?: string;
    adCategory?: "general" | "real-estate" | "automotive" | "services" | "jobs" | "technology" | "health" | "education" | "finance";
    priority?: number;
    startDate?: string;
    endDate?: string;
    targetAudience?: {
      locations?: Array<string>;
      ageGroups?: Array<"18-24" | "25-34" | "35-44" | "45-54" | "55-64" | "65+">;
      interests?: Array<string>;
    };
    isActive?: boolean;
    _type: "sponsoredAd";
    _key: string;
  }>;
  networkAds?: {
    adNetwork?: "adsense" | "media-net" | "amazon" | "facebook" | "custom";
    adUnitId?: string;
    adSlotId?: string;
    customAdCode?: string;
    adSize?: "banner" | "leaderboard" | "rectangle" | "large-rectangle" | "skyscraper" | "mobile-banner" | "responsive" | "custom";
    customWidth?: number;
    customHeight?: number;
    adFormat?: "display" | "text" | "image" | "video" | "native" | "auto";
  };
  adLayout?: {
    displayStyle?: "single" | "carousel" | "grid" | "list";
    adsPerRow?: 1 | 2 | 3 | 4;
    maxAdsToShow?: number;
    autoRotate?: boolean;
    rotationInterval?: number;
  };
  adStyle?: {
    backgroundColor?: "transparent" | "white" | "light-gray" | "custom";
    customBackgroundColor?: string;
    borderRadius?: "none" | "small" | "medium" | "large";
    showAdLabel?: boolean;
    adLabelText?: string;
    sectionPadding?: "none" | "small" | "medium" | "large";
  };
  responsiveSettings?: {
    hideOnMobile?: boolean;
    mobileLayout?: "desktop" | "single" | "stacked";
  };
  analytics?: {
    trackClicks?: boolean;
    trackImpressions?: boolean;
    customTrackingCode?: string;
  };
  publishedAt?: string;
};

export type FeaturedListingsSection = {
  _id: string;
  _type: "featuredListingsSection";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  isActive?: boolean;
  displayOrder?: number;
  subtitle?: string;
  filterConfiguration?: {
    showCategoryFilter?: boolean;
    defaultFilterText?: string;
    filterCategories?: Array<{
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      _key: string;
      [internalGroqTypeReferenceTo]?: "category";
    }>;
    showLocationFilter?: boolean;
    showPriceFilter?: boolean;
  };
  listingSelection?: {
    selectionMethod?: "manual" | "auto-featured" | "auto-recent-premium" | "auto-most-viewed" | "auto-highest-priced" | "mixed";
    manualListings?: Array<{
      listingReference?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "listing";
      };
      customTitle?: string;
      customImage?: {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        alt?: string;
        _type: "image";
      };
      cardSize?: "small" | "medium" | "large" | "xl";
      priority?: number;
      isActive?: boolean;
      _type: "featuredListing";
      _key: string;
    }>;
    autoSelectionCriteria?: {
      maxListings?: number;
      excludeCategories?: Array<{
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        _key: string;
        [internalGroqTypeReferenceTo]?: "category";
      }>;
      minPrice?: number;
      maxAge?: number;
      requireImages?: boolean;
    };
  };
  layoutConfiguration?: {
    layoutStyle?: "masonry" | "grid" | "list";
    columnsPerRow?: {
      desktop?: 3 | 4 | 5 | 6;
      tablet?: 2 | 3 | 4;
      mobile?: 1 | 2;
    };
    cardSpacing?: "tight" | "normal" | "loose";
    cardAspectRatios?: {
      small?: "1:1" | "3:4" | "4:3";
      medium?: "1:1" | "3:4" | "4:3";
      large?: "1:1" | "3:4" | "4:3" | "16:9";
    };
  };
  cardConfiguration?: {
    showPrice?: boolean;
    priceFormat?: "full" | "abbreviated" | "compact";
    showLocation?: boolean;
    showCategory?: boolean;
    showFeaturedBadge?: boolean;
    hoverEffect?: "none" | "scale" | "lift" | "overlay";
    cardStyle?: {
      borderRadius?: "none" | "small" | "medium" | "large";
      shadow?: "none" | "small" | "medium" | "large";
      border?: "none" | "light" | "medium";
    };
  };
  sectionStyle?: {
    backgroundColor?: "white" | "light-gray" | "off-white" | "custom";
    customBackgroundColor?: string;
    padding?: "small" | "medium" | "large";
    titleAlignment?: "left" | "center" | "right";
  };
  callToAction?: {
    showviewAllListingsButton?: boolean;
    viewAllListingsButtonText?: string;
    viewAllListingsButtonUrl?: string;
    buttonStyle?: "primary" | "secondary" | "outline";
  };
  analytics?: {
    trackListingViews?: boolean;
    trackListingClicks?: boolean;
    trackFilterUsage?: boolean;
  };
  publishedAt?: string;
};

export type FeaturedCategoriesSection = {
  _id: string;
  _type: "featuredCategoriesSection";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  isActive?: boolean;
  displayOrder?: number;
  subtitle?: string;
  categories?: Array<{
    name?: string;
    slug?: Slug;
    description?: string;
    categoryReference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "category";
    };
    url?: string;
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      alt?: string;
      _type: "image";
    };
    icon?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      alt?: string;
      _type: "image";
    };
    cardStyle?: {
      backgroundColor?: "white" | "light-gray" | "light-blue" | "light-green" | "light-purple" | "custom";
      customBackgroundColor?: string;
      borderStyle?: "none" | "light" | "medium" | "thick";
      borderRadius?: "none" | "small" | "medium" | "large" | "full";
      shadow?: "none" | "small" | "medium" | "large";
    };
    hoverEffect?: {
      enabled?: boolean;
      scaleOnHover?: boolean;
      shadowOnHover?: boolean;
    };
    statistics?: {
      showAdCount?: boolean;
      listingCount?: number;
      listingCountLabel?: string;
    };
    isActive?: boolean;
    sortOrder?: number;
    featured?: "normal" | "high" | "premium";
    analytics?: {
      trackingId?: string;
      conversionGoal?: string;
    };
    _type: "featuredCategory";
    _key: string;
  }>;
  layoutConfiguration?: {
    cardsPerRow?: {
      desktop?: 2 | 3 | 4 | 5 | 6;
      tablet?: 2 | 3;
      mobile?: 1 | 2;
    };
    cardAspectRatio?: "1:1" | "3:4" | "4:3" | "16:9";
    cardSpacing?: "tight" | "normal" | "loose";
    imagePosition?: "top" | "center" | "bottom";
  };
  sectionStyle?: {
    backgroundColor?: "white" | "light-gray" | "off-white" | "custom";
    customBackgroundColor?: string;
    padding?: "small" | "medium" | "large";
    titleAlignment?: "left" | "center" | "right";
  };
  callToAction?: {
    showViewAllButton?: boolean;
    viewAllButtonText?: string;
    viewAllButtonUrl?: string;
  };
  publishedAt?: string;
};

export type FeaturedServicesSection = {
  _id: string;
  _type: "featuredServicesSection";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  isActive?: boolean;
  displayOrder?: number;
  showSectionTitle?: boolean;
  services?: Array<{
    title?: string;
    slug?: Slug;
    description?: string;
    features?: Array<{
      text?: string;
      icon?: "checkmark" | "star" | "arrow" | "shield" | "clock" | "custom";
      customIcon?: {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: "image";
      };
      _type: "feature";
      _key: string;
    }>;
    callToAction?: {
      text?: string;
      url?: string;
      style?: "primary" | "secondary" | "outline" | "ghost";
      openInNewTab?: boolean;
    };
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      alt?: string;
      _type: "image";
    };
    slideStyle?: {
      backgroundColor?: "white" | "light-gray" | "light-blue" | "light-green" | "custom";
      customBackgroundColor?: string;
      textColor?: "dark-gray" | "black" | "white" | "custom";
      customTextColor?: string;
    };
    layout?: {
      contentPosition?: "left-content" | "right-content" | "center-content";
      contentWidth?: "40-60" | "50-50" | "60-40";
    };
    isActive?: boolean;
    sortOrder?: number;
    analytics?: {
      trackingId?: string;
      conversionGoal?: string;
    };
    _type: "featuredService";
    _key: string;
  }>;
  sliderConfiguration?: {
    autoplay?: boolean;
    autoplaySpeed?: number;
    showNavigation?: boolean;
    showDots?: boolean;
    pauseOnHover?: boolean;
    transitionEffect?: "slide" | "fade" | "zoom";
    transitionSpeed?: number;
  };
  sectionStyle?: {
    containerMaxWidth?: "full" | "xl" | "lg" | "md";
    padding?: "small" | "medium" | "large";
    borderRadius?: "none" | "small" | "medium" | "large";
    shadow?: "none" | "small" | "medium" | "large";
  };
  responsiveSettings?: {
    mobileLayout?: "stack" | "side-by-side";
    hideOnMobile?: boolean;
  };
  publishedAt?: string;
};

export type MoreFromOlySection = {
  _id: string;
  _type: "moreFromOlySection";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  isActive?: boolean;
  displayOrder?: number;
  subtitle?: string;
  sites?: Array<{
    name?: string;
    slug?: Slug;
    description?: string;
    url?: string;
    openInNewTab?: boolean;
    backgroundImage?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      alt?: string;
      _type: "image";
    };
    cardStyle?: {
      backgroundColor?: "teal" | "gray" | "blue" | "black" | "green" | "purple" | "red" | "custom";
      customBackgroundColor?: string;
      textColor?: "white" | "black" | "gray";
      overlayOpacity?: number;
    };
    icon?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      alt?: string;
      _type: "image";
    };
    isActive?: boolean;
    sortOrder?: number;
    analytics?: {
      trackingId?: string;
      category?: "properties" | "automotive" | "jobs" | "services" | "general";
    };
    _type: "networkSite";
    _key: string;
  }>;
  layoutConfiguration?: {
    cardsPerRow?: {
      desktop?: 2 | 3 | 4 | 5;
      tablet?: 2 | 3;
      mobile?: 1 | 2;
    };
    cardAspectRatio?: "1:1" | "3:4" | "4:3" | "16:9";
    cardSpacing?: "tight" | "normal" | "loose";
  };
  sectionStyle?: {
    backgroundColor?: "white" | "light-gray" | "dark-gray" | "custom";
    customBackgroundColor?: string;
    padding?: "small" | "medium" | "large";
    titleAlignment?: "left" | "center" | "right";
  };
  publishedAt?: string;
};

export type HeroSection = {
  _id: string;
  _type: "heroSection";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  isActive?: boolean;
  showBrandNewBadge?: boolean;
  badgeText?: string;
  badgeColor?: "cyan" | "blue" | "green" | "yellow" | "red" | "purple";
  mainHeading?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  subheading?: string;
  primaryButton?: {
    text?: string;
    url?: string;
    style?: "primary" | "secondary" | "outline";
    openInNewTab?: boolean;
  };
  secondaryButton?: {
    text?: string;
    url?: string;
    style?: "primary" | "secondary" | "outline";
    openInNewTab?: boolean;
  };
  searchConfiguration?: {
    showSearch?: boolean;
    searchPlaceholder?: string;
    locationPlaceholder?: string;
    searchButtonText?: string;
  };
  backgroundStyle?: {
    backgroundColor?: "light-gray" | "light-blue" | "white" | "custom";
    customBackgroundColor?: string;
    showCircularElement?: boolean;
    circularElementColor?: "light-blue" | "light-green" | "light-purple" | "light-yellow";
  };
  layout?: {
    textAlignment?: "left" | "center" | "right";
    contentLayout?: "split" | "centered" | "full-width";
  };
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
  };
  publishedAt?: string;
};

export type SiteLogo = {
  _id: string;
  _type: "siteLogo";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  site?: "oly" | "oly-auto" | "oly-properties" | "oly-services" | "oly-hiring";
  logoLight?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  logoDark?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  logoMonochrome?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  logoMobile?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  logoSVG?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    _type: "file";
  };
  favicon?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  alt?: string;
  updatedAt?: string;
};

export type NotificationSettings = {
  _id: string;
  _type: "notificationSettings";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  userId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  channels?: {
    email?: boolean;
    push?: boolean;
    sms?: boolean;
    inApp?: boolean;
  };
  categories?: {
    messages?: boolean;
    replies?: boolean;
    ads?: boolean;
    promotion?: boolean;
    security?: boolean;
    newsletter?: boolean;
  };
  digestFrequency?: "instant" | "hourly" | "daily" | "weekly" | "off";
  quietHours?: {
    enabled?: boolean;
    start?: "0:00" | "1:00" | "2:00" | "3:00" | "4:00" | "5:00" | "6:00" | "7:00" | "8:00" | "9:00" | "10:00" | "11:00" | "12:00" | "13:00" | "14:00" | "15:00" | "16:00" | "17:00" | "18:00" | "19:00" | "20:00" | "21:00" | "22:00" | "23:00";
    end?: "0:00" | "1:00" | "2:00" | "3:00" | "4:00" | "5:00" | "6:00" | "7:00" | "8:00" | "9:00" | "10:00" | "11:00" | "12:00" | "13:00" | "14:00" | "15:00" | "16:00" | "17:00" | "18:00" | "19:00" | "20:00" | "21:00" | "22:00" | "23:00";
  };
  language?: "en" | "af" | "zu" | "xh" | "st";
  lastUpdated?: string;
};

export type Notification = {
  _id: string;
  _type: "notification";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  userId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  message?: string;
  type?: "info" | "success" | "warning" | "error" | "promotion" | "system" | "chat" | "new-listing" | "booking" | "auction" | "reminder";
  status?: "unread" | "read" | "archived";
  actionLabel?: string;
  actionUrl?: string;
  referenceType?: "listing" | "chat" | "user" | "auction" | "none";
  referenceId?: string;
  site?: "oly" | "oly-properties" | "oly-auto" | "oly-hiring" | "oly-services";
  sentAt?: string;
  readAt?: string;
  delivered?: boolean;
  deliveryAttempts?: number;
  priority?: "low" | "medium" | "high";
};

export type Coupon = {
  _id: string;
  _type: "coupon";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  code?: string;
  description?: string;
  discountType?: "percentage" | "fixed";
  discountValue?: number;
  minOrderAmount?: number;
  maxDiscountValue?: number;
  startDate?: string;
  expirationDate?: string;
  usageLimit?: number;
  usageLimitPerUser?: number;
  applicableSites?: Array<string>;
  listingPackages?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "listingPackage";
  }>;
  firstTimeUsersOnly?: boolean;
  isActive?: boolean;
  internalNotes?: string;
};

export type Promotion = {
  _id: string;
  _type: "promotion";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: string;
  startDate?: string;
  endDate?: string;
  isActive?: boolean;
  targetSites?: Array<string>;
  promoCode?: string;
  listings?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "listing";
  }>;
  bannerImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  priority?: number;
  createdBy?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  internalNotes?: string;
};

export type SeoMetadata = {
  _id: string;
  _type: "seoMetadata";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  pageId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "page";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "listing";
  };
  site?: "oly" | "oly-properties" | "oly-auto" | "oly-hiring" | "oly-services";
  slugOverride?: Slug;
  title?: string;
  description?: string;
  keywords?: Array<string>;
  ogImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  twitterCardType?: "summary" | "summary_large_image";
  structuredData?: string;
  lastUpdated?: string;
};

export type FlaggedContent = {
  _id: string;
  _type: "flaggedContent";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  contentId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "listing";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "review";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "message";
  };
  snapshot?: string;
  flagType?: "manual" | "automated";
  reason?: string;
  flaggedBy?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  automatedDetectionMetadata?: {
    detectedBy?: string;
    confidenceScore?: number;
  };
  date?: string;
  status?: "pending" | "under_review" | "resolved" | "rejected";
  severity?: "low" | "medium" | "high";
  resolutionAction?: "removed" | "edited" | "approved" | "no_action" | "escalated";
  reviewedBy?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  reviewDate?: string;
  notes?: string;
  escalatedTo?: string;
  reviewLog?: Array<{
    action?: string;
    timestamp?: string;
    by?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "user";
    };
    note?: string;
    _key: string;
  }>;
  relatedFlags?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "flaggedContent";
  }>;
  site?: "oly" | "oly-properties" | "oly-auto" | "oly-hiring";
};

export type ModerationLog = {
  _id: string;
  _type: "moderationLog";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  moderatorId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  action?: "approve" | "reject" | "flag" | "delete" | "edit" | "hide" | "unhide" | "restore";
  contentType?: "listing" | "review" | "message" | "comment" | "userProfile";
  contentId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "listing";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "review";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "message";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "moderationComment";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  reason?: string;
  notes?: string;
  previousState?: string;
  newState?: string;
  automated?: boolean;
  date?: string;
  site?: "oly" | "oly-auto" | "oly-properties" | "oly-hiring" | "oly-services";
};

export type ModerationComment = {
  _id: string;
  _type: "moderationComment";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  author?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  content?: string;
  createdAt?: string;
};

export type MakeModel = {
  _id: string;
  _type: "makeModel";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  make?: string;
  model?: string;
  variant?: string;
  fuelType?: "Petrol" | "Diesel" | "Electric" | "Hybrid";
  bodyType?: "Hatchback" | "Sedan" | "SUV" | "Bakkie" | "Coupe" | "Van";
};

export type ListingPerformance = {
  _id: string;
  _type: "listingPerformance";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  listingId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "listing";
  };
  views?: number;
  clicks?: number;
  impressions?: number;
  conversions?: number;
  ctr?: number;
  period?: "daily" | "weekly" | "monthly" | "lifetime";
  date?: string;
  site?: "oly" | "oly-auto" | "oly-properties" | "oly-hiring" | "oly-services";
  source?: "organic" | "search" | "social" | "email" | "paid";
};

export type UserEngagement = {
  _id: string;
  _type: "userEngagement";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  user?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  site?: "oly" | "oly-properties" | "oly-auto" | "oly-hiring" | "oly-services";
  sessionId?: string;
  eventType?: "page_view" | "search" | "click" | "message_sent" | "ad_saved" | "ad_shared" | "phone_viewed";
  eventTargetId?: string;
  deviceType?: "desktop" | "mobile" | "tablet" | "unknown";
  browser?: string;
  location?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "location";
  };
  timestamp?: string;
};

export type ServiceDetails = {
  _id: string;
  _type: "serviceDetails";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  price?: number;
  priceUnit?: "per hour" | "per job" | "per day" | "starting from" | "negotiable";
  location?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "location";
  };
  category?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "category";
  };
  availability?: string;
  images?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
    _key: string;
  }>;
  tags?: Array<string>;
  provider?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "storeOwner";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "professionalServiceProvider";
  };
  listingPackage?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "listingPackage";
  };
  isFeatured?: boolean;
  status?: "active" | "inactive" | "pending";
  createdAt?: string;
  updatedAt?: string;
};

export type ProfessionalServiceProvider = {
  _id: string;
  _type: "professionalServiceProvider";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  user?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  verified?: boolean;
};

export type StoreOwner = {
  _id: string;
  _type: "storeOwner";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  user?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  storeName?: string;
  slug?: Slug;
  logo?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  banner?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  description?: string;
  location?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "location";
  };
  contactNumber?: string;
  email?: string;
  verified?: boolean;
  storeCategories?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "category";
  }>;
  createdAt?: string;
};

export type JobDetails = {
  _id: string;
  _type: "jobDetails";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  employmentType?: "full-time" | "part-time" | "contract" | "temporary" | "internship" | "remote";
  salary?: number;
  salaryType?: "negotiable" | "fixed" | "commission";
  company?: string;
  companyLogo?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  location?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "location";
  };
  remoteAvailable?: boolean;
  category?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "category";
  };
  qualifications?: Array<string>;
  experienceLevel?: "entry" | "mid" | "senior" | "executive";
  applicationDeadline?: string;
  applyLink?: string;
  contactEmail?: string;
  postedBy?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "dealerProfile";
  };
  listingPackage?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "listingPackage";
  };
  isFeatured?: boolean;
  slug?: Slug;
  datePosted?: string;
};

export type DealerProfile = {
  _id: string;
  _type: "dealerProfile";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  email?: string;
  phone?: string;
  logo?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  location?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "location";
  };
  website?: string;
};

export type PropertyDetails = {
  _id: string;
  _type: "propertyDetails";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: string;
  propertyType?: "house" | "apartment" | "townhouse" | "duplex" | "sectional-title" | "land" | "farm" | "commercial" | "industrial";
  listingType?: "for-sale" | "to-rent" | "student" | "shared";
  price?: number;
  levies?: number;
  ratesAndTaxes?: number;
  beds?: number;
  baths?: number;
  parking?: number;
  province?: "Eastern Cape" | "Free State" | "Gauteng" | "KwaZulu-Natal" | "Limpopo" | "Mpumalanga" | "Northern Cape" | "North West" | "Western Cape";
  municipality?: string;
  suburb?: string;
  address?: string;
  geoLocation?: Geopoint;
  images?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
    _key: string;
  }>;
  videos?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "videoFile";
  }>;
  features?: Array<string>;
  isPetFriendly?: boolean;
  availableFrom?: string;
  attachments?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    _type: "file";
    _key: string;
  }>;
  listedBy?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  contactPhone?: string;
  contactEmail?: string;
  isFeatured?: boolean;
  status?: "active" | "sold" | "rented" | "off-market";
  dateListed?: string;
};

export type BankDetails = {
  _type: "bankDetails";
  bankName?: string;
  accountHolderName?: string;
  accountNumber?: string;
  branchCode?: string;
  bankCode?: string;
  swiftCode?: string;
};

export type AuctionTest = {
  _id: string;
  _type: "auctionTest";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  imageUrl?: string;
  startTime?: string;
  endTime?: string;
  currentBid?: number;
  highestBidder?: string;
};

export type Attachment = {
  _id: string;
  _type: "attachment";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  user?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  attachment?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    fileName?: string;
    originalFilename?: string;
    _type: "file";
  };
};

export type VideoFile = {
  _id: string;
  _type: "videoFile";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  user?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  video?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    alt?: string;
    originalFilename?: string;
    location?: Geopoint;
    _type: "file";
  };
};

export type InspectionReport = {
  _id: string;
  _type: "inspectionReport";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  vehicle?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "vehicleDetails";
  };
  reportFile?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    _type: "file";
  };
  inspectedAt?: string;
  inspectorName?: string;
  inspectionResult?: string;
  source?: string;
};

export type VehicleDetails = {
  _id: string;
  _type: "vehicleDetails";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: string;
  make?: string;
  model?: string;
  variant?: string;
  year?: number;
  mileage?: number;
  price?: number;
  bodyType?: string;
  fuelType?: string;
  transmission?: string;
  driveType?: string;
  engineSize?: number;
  power?: number;
  colour?: string;
  interiorColour?: string;
  serviceHistory?: "Full Service History" | "Partial" | "None" | "Unknown";
  accidentHistory?: "None" | "Minor" | "Major" | "Unknown";
  condition?: "Excellent" | "Good" | "Fair" | "Needs Work";
  registrationNumber?: string;
  vinNumber?: string;
  dealerName?: string;
  dealerLicenseNumber?: string;
  isFleetVehicle?: boolean;
  isVATInclusive?: boolean;
  images?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
    _key: string;
  }>;
  videos?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "videoFile";
  }>;
  province?: string;
  suburb?: string;
  location?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "location";
  };
  geoLocation?: Geopoint;
  features?: Array<string>;
  ownership?: "private" | "dealer";
  isFinanceAvailable?: boolean;
  tradeInAccepted?: boolean;
  contactName?: string;
  contactPhone?: string;
  contactEmail?: string;
  status?: "available" | "sold" | "pending" | "rejected";
  moderationNotes?: string;
  isFeatured?: boolean;
  dateListed?: string;
  expiryDate?: string;
};

export type ImageFile = {
  _id: string;
  _type: "imageFile";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  user?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    alt?: string;
    originalFilename?: string;
    location?: Geopoint;
    _type: "image";
  };
};

export type Details = {
  _id: string;
  _type: "details";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  condition?: string;
  accessoriesIncluded?: Array<string>;
  warrantyInformation?: string;
  history?: string;
  customizations?: string;
  maintenanceHistory?: string;
  compatibility?: string;
  originalPackaging?: string;
  usageHistory?: string;
  storage?: string;
  originalPurchaseDate?: string;
  reasonForSelling?: string;
  additionalFeatures?: string;
  serviceRecords?: string;
  userManualAvailability?: string;
  manufacturerSupport?: string;
  compatibilityWithAccessories?: string;
  packagingCondition?: string;
  productHistory?: string;
  transferability?: string;
  petSmokeExposure?: string;
  regulatoryCompliance?: string;
  specialFeatures?: string;
  documentation?: string;
  certification?: string;
  age?: string;
  ownership?: string;
  environmentalImpact?: string;
  knownIssues?: string;
  upgrades?: string;
};

export type Page = {
  _id: string;
  _type: "page";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  slug?: Slug;
  title?: string;
  description?: string;
  content?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  mainImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  seoTitle?: string;
  seoDescription?: string;
  publishedAt?: string;
  categories?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "category";
  }>;
  tags?: Array<string>;
  relatedPages?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "page";
  }>;
  callToAction?: string;
  socialMediaLinks?: Array<string>;
  commentsEnabled?: boolean;
  metaRobots?: string;
  canonicalUrl?: string;
  structuredData?: string;
  customScripts?: Array<string>;
};

export type Slide = {
  _type: "slide";
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  text?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
};

export type SponsoredArticles = {
  _type: "SponsoredArticles";
  title?: string;
};

export type Blog = {
  _type: "Blog";
  title?: string;
};

export type FeaturedCategories = {
  _type: "FeaturedCategories";
  title?: string;
};

export type FeaturedListings = {
  _type: "FeaturedListings";
  title?: string;
};

export type Features = {
  _type: "Features";
  title?: string;
  slide?: Array<{
    _key: string;
  } & Slide>;
};

export type SocialMediaLink = {
  _type: "socialMediaLink";
  platform?: string;
  url?: string;
  icon?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
};

export type Footer = {
  _id: string;
  _type: "Footer";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  socialMediaLinks?: Array<{
    _key: string;
  } & SocialMediaLink>;
  copyright?: string;
};

export type Upload = {
  _id: string;
  _type: "upload";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  video?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    _type: "file";
  };
  attachment?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    _type: "file";
  };
  file?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    _type: "file";
  };
  description?: string;
  uploadedBy?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  uploadDate?: string;
  tags?: Array<string>;
  preview?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
};

export type AuditEntry = {
  _type: "auditEntry";
  actionDate?: string;
  action?: string;
  actionBy?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  comments?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
};

export type MessageFlag = {
  _id: string;
  _type: "messageFlag";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  message?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "message";
  };
  flaggedBy?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  reason?: string;
  details?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  flaggedAt?: string;
  status?: "pending" | "reviewed" | "action_taken" | "dismissed";
  violationType?: "harassment" | "spam" | "inappropriate_content" | "hate_speech" | "other";
  reviewedBy?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  reviewedAt?: string;
  actionTaken?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  systemFlagged?: boolean;
  userFeedback?: {
    satisfied?: boolean;
    feedbackComments?: Array<{
      children?: Array<{
        marks?: Array<string>;
        text?: string;
        _type: "span";
        _key: string;
      }>;
      style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
      listItem?: "bullet" | "number";
      markDefs?: Array<{
        href?: string;
        _type: "link";
        _key: string;
      }>;
      level?: number;
      _type: "block";
      _key: string;
    }>;
  };
  auditTrail?: Array<{
    _key: string;
  } & AuditEntry>;
};

export type Message = {
  _id: string;
  _type: "message";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  conversation?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "conversation";
  };
  sender?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  timestamp?: string;
  text?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  reactions?: Array<{
    type?: string;
    user?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "user";
    };
    _type: "reaction";
    _key: string;
  }>;
  replies?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "messageReply";
  }>;
  uploads?: Array<{
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    video?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
      };
      media?: unknown;
      _type: "file";
    };
    attachment?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
      };
      media?: unknown;
      _type: "file";
    };
    file?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
      };
      media?: unknown;
      _type: "file";
    };
    description?: string;
    uploadedBy?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "user";
    };
    uploadDate?: string;
    tags?: Array<string>;
    preview?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _type: "upload";
    _key: string;
  }>;
  status?: "sent" | "delivered" | "read";
  flags?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "messageFlag";
  }>;
  metrics?: {
    views?: number;
    interactions?: number;
  };
};

export type Conversation = {
  _id: string;
  _type: "conversation";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  participants?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "user";
  }>;
  startedBy?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  createdAt?: string;
  updatedAt?: string;
  archived?: boolean;
  isGroup?: boolean;
  groupName?: string;
  groupIcon?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  messages?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "message";
  }>;
};

export type Group = {
  _id: string;
  _type: "group";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  category?: string;
  members?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "user";
  }>;
  createdBy?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  createdAt?: string;
  updatedAt?: string;
  isActive?: boolean;
};

export type Review = {
  _id: string;
  _type: "review";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  reviewer?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  subject?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "listing";
  } | {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "auctionLot";
  };
  rating?: number;
  title?: string;
  content?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  date?: string;
  response?: {
    respondent?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "user";
    };
    responseContent?: Array<{
      children?: Array<{
        marks?: Array<string>;
        text?: string;
        _type: "span";
        _key: string;
      }>;
      style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
      listItem?: "bullet" | "number";
      markDefs?: Array<{
        href?: string;
        _type: "link";
        _key: string;
      }>;
      level?: number;
      _type: "block";
      _key: string;
    }>;
    responseDate?: string;
  };
  verifiedPurchase?: boolean;
};

export type Transaction = {
  _id: string;
  _type: "transaction";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  transactionId?: string;
  user?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  listings?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "listing";
  }>;
  transactionType?: "purchase" | "sale" | "refund";
  amount?: number;
  currency?: string;
  paymentMethod?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "paymentMethod";
  };
  transactionDate?: string;
  paymentStatus?: "paid" | "unpaid" | "partiallyPaid" | "overdue";
  status?: "pending" | "completed" | "failed" | "cancelled";
  relatedListing?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "listing";
  };
  notes?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  receipt?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    _type: "file";
  };
  category?: "adPurchase" | "subscriptionFee" | "serviceFee";
  seller?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  buyer?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  taxDetails?: {
    amount?: number;
    taxId?: string;
  };
  disputeStatus?: "none" | "pending" | "resolved";
  disputeDetails?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  refundAmount?: number;
  refundDate?: string;
  transactionAttachments?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    _type: "file";
    _key: string;
  }>;
  invoiceNumber?: string;
  paymentDueDate?: string;
  paymentTerms?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  paymentReceipt?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.fileAsset";
    };
    media?: unknown;
    _type: "file";
  };
  shippingDetails?: {
    shippingAddress?: string;
    shippingMethod?: string;
    trackingNumber?: string;
  };
  cancellationReason?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
};

export type Subscription = {
  _id: string;
  _type: "subscription";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  userId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  subscriptionType?: "basic" | "premium";
  startDate?: string;
  endDate?: string;
  paymentMethod?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "paymentMethod";
  };
  status?: "active" | "expired" | "cancelled";
  renewalReminder?: boolean;
  features?: Array<string>;
  billingCycle?: string;
  amount?: number;
  notes?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  autoRenewal?: boolean;
  usageStatistics?: {
    lastAccess?: string;
    frequencyOfUse?: number;
  };
  supportTier?: string;
  additionalBenefits?: Array<string>;
  feedback?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  upgradeOptions?: Array<string>;
};

export type PaymentMethod = {
  _id: string;
  _type: "paymentMethod";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  userId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  methodType?: "creditCard" | "debitCard" | "paypal" | "bankTransfer";
  details?: {
    cardNumber?: string;
    cardHolderName?: string;
    expiryDate?: string;
    securityCode?: string;
  };
  billingAddress?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "address";
  };
  isDefault?: boolean;
  verificationStatus?: "verified" | "pending" | "unverified";
  currency?: string;
  transactionHistory?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "transaction";
  }>;
  paymentGateway?: string;
  isActive?: boolean;
  addedDate?: string;
  additionalSettings?: {
    autoPay?: boolean;
  };
  notes?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  securityFeatures?: {
    fingerprint?: boolean;
    twoFactorAuth?: boolean;
  };
  limits?: {
    dailyLimit?: number;
    transactionLimit?: number;
  };
  rewards?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  expiryNotification?: boolean;
  supportContact?: string;
  customizationOptions?: {
    transactionNotifications?: boolean;
    paymentReminders?: boolean;
    securityAlerts?: boolean;
    balanceUpdates?: boolean;
    emailNotifications?: boolean;
  };
};

export type Address = {
  _id: string;
  _type: "address";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  isBillingAddress?: boolean;
  isShippingAddress?: boolean;
  userId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  additionalInfo?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  location?: Geopoint;
  contactPhone?: string;
  contactEmail?: string;
};

export type AuctionLot = {
  _id: string;
  _type: "auctionLot";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  biddingHistory?: Array<{
    amount?: number;
    bidder?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "user";
    };
    bidDate?: string;
    associatedListing?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "listing";
    };
    isLive?: boolean;
    bidStartTime?: string;
    bidDropOutTime?: string;
    bidStatus?: "bidding" | "dropped-out";
    _type: "bid";
    _key: string;
  }>;
  auctionStartDate?: string;
  shippingDetails?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  returnPolicy?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  lotStatus?: "active" | "closed" | "sold" | "unsold";
  buyer?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  sellerNotes?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  currentHighestBid?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "bid";
  };
  auctionEndDate?: string;
  paymentTerms?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
};

export type UserLocationConsent = {
  _id: string;
  _type: "userLocationConsent";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  userId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  consentGiven?: boolean;
  consentTimestamp?: string;
  consentDetails?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
};

export type RegionalTrends = {
  _id: string;
  _type: "regionalTrends";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  region?: string;
  trendData?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "trendData";
  };
};

export type TrendData = {
  _id: string;
  _type: "trendData";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  popularCategories?: Array<string>;
  averagePrice?: {
    category?: string;
    value?: number;
  };
  searchVolume?: {
    keyword?: string;
    count?: number;
  };
  userEngagement?: {
    pageViews?: number;
    timeSpent?: number;
  };
  listingPostings?: {
    totalListings?: number;
    listingsPerCategory?: {
      category?: string;
      count?: number;
    };
  };
  demographics?: {
    ageGroups?: Array<string>;
    genderRatio?: string;
  };
  marketGrowth?: {
    yearlyGrowth?: number;
  };
};

export type LocationBasedRecommendationSettings = {
  _id: string;
  _type: "locationBasedRecommendationSettings";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  userId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  enableRecommendations?: boolean;
};

export type LocationHistory = {
  _id: string;
  _type: "locationHistory";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  userId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  locations?: Array<{
    location?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "location";
    };
    timestamp?: string;
    _key: string;
  }>;
};

export type LocationBasedSearch = {
  _id: string;
  _type: "locationBasedSearch";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  userId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  country?: string;
  stateOrProvince?: string;
  city?: string;
  suburb?: string;
  searchRadius?: number;
  baseLocation?: Geopoint;
  includeNearbyAreas?: boolean;
  filterCriteria?: {
    categories?: Array<{
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      _key: string;
      [internalGroqTypeReferenceTo]?: "category";
    }>;
  };
  savedSearchName?: string;
  lastUsed?: string;
};

export type LocationBasedListingTargeting = {
  _id: string;
  _type: "locationBasedListingTargeting";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  listingId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "listing";
  };
  targetLocations?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "location";
  }>;
  exclusionZones?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "location";
  }>;
};

export type ListingPackage = {
  _id: string;
  _type: "listingPackage";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  price?: number;
  isFeatured?: boolean;
  durationDays?: number;
  highlightColor?: string;
  description?: string;
};

export type BidderHistory = {
  _id: string;
  _type: "bidderHistory";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  bidder?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  bids?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "bid";
  }>;
};

export type Listing = {
  _id: string;
  _type: "listing";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  category?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "category";
  };
  user?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  site?: "oly" | "oly-properties" | "oly-auto" | "oly-hiring" | "oly-services";
  isActive?: boolean;
  approvedForSale?: "approved" | "pending" | "denied";
  featured?: boolean;
  price?: number;
  currency?: "ZAR" | "USD" | "EUR" | "GBP";
  pricingOption?: "negotiable" | "free" | "auction" | "fixed_price" | "contact_for_price";
  priceId?: string;
  paystackId?: string;
  images?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "imageFile";
  }>;
  featuredImage?: string;
  videos?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "videoFile";
  }>;
  attachments?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "attachment";
  }>;
  location?: Location;
  condition?: "new" | "like-new" | "gently-used" | "used";
  quantity?: number;
  details?: Array<{
    condition?: string;
    accessoriesIncluded?: Array<string>;
    warrantyInformation?: string;
    history?: string;
    customizations?: string;
    maintenanceHistory?: string;
    compatibility?: string;
    originalPackaging?: string;
    usageHistory?: string;
    storage?: string;
    originalPurchaseDate?: string;
    reasonForSelling?: string;
    additionalFeatures?: string;
    serviceRecords?: string;
    userManualAvailability?: string;
    manufacturerSupport?: string;
    compatibilityWithAccessories?: string;
    packagingCondition?: string;
    productHistory?: string;
    transferability?: string;
    petSmokeExposure?: string;
    regulatoryCompliance?: string;
    specialFeatures?: string;
    documentation?: string;
    certification?: string;
    age?: string;
    ownership?: string;
    environmentalImpact?: string;
    knownIssues?: string;
    upgrades?: string;
    _type: "details";
    _key: string;
  }>;
  features?: Array<string>;
  avatar?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  promotions?: Array<{
    platform?: "oly" | "facebook" | "google-ads" | "instagram" | "linkedin";
    duration?: "1day" | "7days" | "2weeks" | "1month" | "3months";
    remainingDays?: number;
    _key: string;
  }>;
  bids?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "bid";
  }>;
  associatedAuction?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "auction";
  };
  likes?: number;
  todaysViews?: number;
  totalViews?: number;
  unreadMessages?: number;
  postedOn?: string;
  expiresAt?: string;
};

export type Auction = {
  _id: string;
  _type: "auction";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  startingPrice?: number;
  reservePrice?: number;
  buyNowPrice?: number;
  ending?: string;
  highestBid?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "bid";
  };
  serviceFeeBidder?: number;
  serviceFeeAdvertiser?: number;
  liveBiddingStart?: string;
  duration?: number;
  timeLeft?: number;
  minimumBid?: number;
  biddersList?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "user";
  }>;
  currentLiveBidders?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "user";
  }>;
  totalBidders?: number;
  currentLiveBiddersCount?: number;
  lots?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "auctionLot";
  }>;
  upcoming?: boolean;
  favoritedBy?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "user";
  }>;
  partOfMyAuctions?: boolean;
  buyNowOption?: boolean;
  auctionType?: "live" | "timed" | "buy-now";
  remainingTime?: string;
};

export type Bid = {
  _id: string;
  _type: "bid";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  amount?: number;
  bidder?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  bidDate?: string;
  associatedListing?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "listing";
  };
  isLive?: boolean;
  bidStartTime?: string;
  bidDropOutTime?: string;
  bidStatus?: "bidding" | "dropped-out";
};

export type Category = {
  _id: string;
  _type: "category";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  path?: string;
  parent?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "category";
  };
  subcategories?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "category";
  }>;
  level?: number;
  isRoot?: boolean;
  description?: string;
  icon?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  isActive?: boolean;
  isFeatured?: boolean;
  order?: number;
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: Array<string>;
  };
  customAttributes?: Array<{
    name?: string;
    type?: "text" | "number" | "boolean" | "select" | "date";
    required?: boolean;
    options?: Array<string>;
    _key: string;
  }>;
  itemCount?: number;
};

export type User = {
  _id: string;
  _type: "user";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  id?: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  username?: string;
  profileImage?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  imageUrl?: string;
  hasImage?: boolean;
  emailAddress?: string;
  emailAddresses?: Array<{
    id?: string;
    emailAddress?: string;
    verification?: {
      status?: string;
      strategy?: string;
      externalVerificationRedirectURL?: string;
      attempts?: number;
      expireAt?: number;
      nonce?: string;
    };
    linkedTo?: Array<string>;
    _key: string;
  }>;
  primaryEmailAddressId?: string;
  phoneNumbers?: Array<{
    id?: string;
    _key: string;
  }>;
  primaryPhoneNumberId?: string;
  userType?: "buyer" | "seller" | "professional_seller" | "store_owner" | "service_provider" | "job_seeker" | "employer" | "casual_browser" | "community_member" | "administrator" | "advertiser" | "reader" | "content_creator" | "editor" | "influencer" | "media_pr";
  bio?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  banned?: boolean;
  lastSignInAt?: string;
  postedListings?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "listing";
  }>;
  transactions?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "transaction";
  }>;
  participatedAuctions?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "auction";
  }>;
  bids?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "bid";
  }>;
  favourites?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "listing";
  }>;
  transactionHistory?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "transaction";
  }>;
  bankAccounts?: Array<{
    _key: string;
  } & BankDetails>;
  reviews?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "review";
  }>;
  messages?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "message";
  }>;
  blocklist?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "user";
  }>;
  isChatGroupAdmin?: boolean;
  passwordEnabled?: boolean;
  totpEnabled?: boolean;
  twoFactorEnabled?: boolean;
  backupCodeEnabled?: boolean;
  updatedAt?: string;
  primaryWeb3WalletId?: string;
  externalId?: string;
  publicMetadata?: {
    firstName?: string;
    lastName?: string;
    age?: number;
    gender?: string;
  };
  privateMetadata?: {
    phoneNumber?: string;
    address?: string;
    creditCardNumber?: string;
    socialSecurityNumber?: string;
  };
  unsafeMetadata?: {
    password?: string;
    encryptedData?: string;
    privateKey?: string;
    securityQuestion?: string;
  };
  web3Wallets?: Array<{
    id?: string;
    _key: string;
  }>;
  externalAccounts?: Array<{
    id?: string;
    _key: string;
  }>;
  createOrganizationEnabled?: boolean;
  notificationSettings?: {
    emailNotifications?: boolean;
    pushNotifications?: boolean;
  };
  privacySettings?: {
    showEmail?: boolean;
    showPhoneNumber?: boolean;
    allowMessaging?: boolean;
    profileVisibility?: "public" | "private";
    currentLocation?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "location";
    };
    locationPreferences?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "userLocationPreference";
    };
    locationAlerts?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "locationAlertSettings";
    };
    languagePreference?: string;
    paymentMethods?: Array<{
      userId?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "user";
      };
      methodType?: "creditCard" | "debitCard" | "paypal" | "bankTransfer";
      details?: {
        cardNumber?: string;
        cardHolderName?: string;
        expiryDate?: string;
        securityCode?: string;
      };
      billingAddress?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "address";
      };
      isDefault?: boolean;
      verificationStatus?: "verified" | "pending" | "unverified";
      currency?: string;
      transactionHistory?: Array<{
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        _key: string;
        [internalGroqTypeReferenceTo]?: "transaction";
      }>;
      paymentGateway?: string;
      isActive?: boolean;
      addedDate?: string;
      additionalSettings?: {
        autoPay?: boolean;
      };
      notes?: Array<{
        children?: Array<{
          marks?: Array<string>;
          text?: string;
          _type: "span";
          _key: string;
        }>;
        style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
        listItem?: "bullet" | "number";
        markDefs?: Array<{
          href?: string;
          _type: "link";
          _key: string;
        }>;
        level?: number;
        _type: "block";
        _key: string;
      }>;
      securityFeatures?: {
        fingerprint?: boolean;
        twoFactorAuth?: boolean;
      };
      limits?: {
        dailyLimit?: number;
        transactionLimit?: number;
      };
      rewards?: Array<{
        children?: Array<{
          marks?: Array<string>;
          text?: string;
          _type: "span";
          _key: string;
        }>;
        style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
        listItem?: "bullet" | "number";
        markDefs?: Array<{
          href?: string;
          _type: "link";
          _key: string;
        }>;
        level?: number;
        _type: "block";
        _key: string;
      }>;
      expiryNotification?: boolean;
      supportContact?: string;
      customizationOptions?: {
        transactionNotifications?: boolean;
        paymentReminders?: boolean;
        securityAlerts?: boolean;
        balanceUpdates?: boolean;
        emailNotifications?: boolean;
      };
      _type: "paymentMethod";
      _key: string;
    }>;
    watchList?: Array<{
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      _key: string;
      [internalGroqTypeReferenceTo]?: "listing";
    }>;
    userRating?: number;
    recentlyViewedListings?: Array<{
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      _key: string;
      [internalGroqTypeReferenceTo]?: "listing";
    }>;
    accountCreationDate?: string;
    lastLogin?: string;
    newsletterSubscription?: boolean;
    marketingPreferences?: {
      receivePromotionalOffers?: boolean;
      preferredCategories?: Array<string>;
    };
    securitySettings?: {
      twoFactorAuthentication?: boolean;
    };
    customPreferences?: {
      displayMode?: "light" | "dark";
      notificationSound?: boolean;
    };
  };
  contactPreferences?: {
    preferredContactMethod?: "email" | "phone" | "in-app";
  };
  membershipLevel?: "standard" | "premium";
  feedback?: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "normal" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "blockquote";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }>;
  referralCode?: string;
  subscribedCategories?: Array<string>;
  registrationSource?: string;
  listingPostPreferences?: {
    defaultListingDuration?: number;
    autoRenewListings?: boolean;
    showExpiredListings?: boolean;
    listingVisibility?: "public" | "private";
  };
  flags?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "messageFlag";
  }>;
  searchSettings?: {
    savedFilters?: Array<string>;
    searchHistory?: Array<string>;
    searchAlerts?: boolean;
    excludeRegions?: Array<string>;
  };
  privacyControls?: {
    hideProfileFromSearch?: boolean;
  };
  accountSettings?: {
    emailVerified?: boolean;
    accountDeletionRequested?: boolean;
    passwordReset?: boolean;
    loginAlerts?: boolean;
    accountPrivacy?: "public" | "private";
    dataDownloadRequest?: boolean;
    marketingConsent?: boolean;
  };
  notificationFilters?: {
    receiveAuctionUpdates?: boolean;
    messageNotifications?: boolean;
    listingStatusUpdates?: boolean;
    bidActivityAlerts?: boolean;
    promotionNotifications?: boolean;
    recommendationAlerts?: boolean;
    newsletterAlerts?: boolean;
  };
};

export type LocationAlertSettings = {
  _id: string;
  _type: "locationAlertSettings";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  userId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  alertLocations?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "location";
  }>;
  alertTypes?: Array<string>;
};

export type UserLocationPreference = {
  _id: string;
  _type: "userLocationPreference";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  userId?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  preferredLocations?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "location";
  }>;
};

export type Location = {
  _type: "location";
  address?: string;
  suburb?: string;
  city?: string;
  province?: "Gauteng" | "Western Cape" | "KwaZulu-Natal" | "Eastern Cape" | "Free State" | "Limpopo" | "Mpumalanga" | "North West" | "Northern Cape";
  region?: string;
  country?: string;
  postalCode?: string;
  coordinates?: Geopoint;
};

export type SanityImagePaletteSwatch = {
  _type: "sanity.imagePaletteSwatch";
  background?: string;
  foreground?: string;
  population?: number;
  title?: string;
};

export type SanityImagePalette = {
  _type: "sanity.imagePalette";
  darkMuted?: SanityImagePaletteSwatch;
  lightVibrant?: SanityImagePaletteSwatch;
  darkVibrant?: SanityImagePaletteSwatch;
  vibrant?: SanityImagePaletteSwatch;
  dominant?: SanityImagePaletteSwatch;
  lightMuted?: SanityImagePaletteSwatch;
  muted?: SanityImagePaletteSwatch;
};

export type SanityImageDimensions = {
  _type: "sanity.imageDimensions";
  height?: number;
  width?: number;
  aspectRatio?: number;
};

export type SanityImageHotspot = {
  _type: "sanity.imageHotspot";
  x?: number;
  y?: number;
  height?: number;
  width?: number;
};

export type SanityImageCrop = {
  _type: "sanity.imageCrop";
  top?: number;
  bottom?: number;
  left?: number;
  right?: number;
};

export type SanityFileAsset = {
  _id: string;
  _type: "sanity.fileAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  source?: SanityAssetSourceData;
};

export type SanityImageAsset = {
  _id: string;
  _type: "sanity.imageAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  metadata?: SanityImageMetadata;
  source?: SanityAssetSourceData;
};

export type SanityImageMetadata = {
  _type: "sanity.imageMetadata";
  location?: Geopoint;
  dimensions?: SanityImageDimensions;
  palette?: SanityImagePalette;
  lqip?: string;
  blurHash?: string;
  hasAlpha?: boolean;
  isOpaque?: boolean;
};

export type Geopoint = {
  _type: "geopoint";
  lat?: number;
  lng?: number;
  alt?: number;
};

export type Slug = {
  _type: "slug";
  current?: string;
  source?: string;
};

export type SanityAssetSourceData = {
  _type: "sanity.assetSourceData";
  name?: string;
  id?: string;
  url?: string;
};

export type AllSanitySchemaTypes = MessageReply | ServiceProvider | RecruitmentAgency | CarDealership | ContentChannel | Company | Store | ServicesListing | ServicesCategory | JobListing | JobCategory | AutoListing | VehicleCategory | PropertyListing | PropertyCategory | ArticleCard | OlyHomepage | SponsoredArticlesSection | Sponsor | SponsoredArticle | OlyArticlesSection | ArticlePage | CommentsSection | ArticleSidebar | ArticleCategory | Author | BottomAdSection | TopAdSection | AdSection | FeaturedListingsSection | FeaturedCategoriesSection | FeaturedServicesSection | MoreFromOlySection | HeroSection | SiteLogo | NotificationSettings | Notification | Coupon | Promotion | SeoMetadata | FlaggedContent | ModerationLog | ModerationComment | MakeModel | ListingPerformance | UserEngagement | ServiceDetails | ProfessionalServiceProvider | StoreOwner | JobDetails | DealerProfile | PropertyDetails | BankDetails | AuctionTest | Attachment | VideoFile | InspectionReport | VehicleDetails | ImageFile | Details | Page | Slide | SponsoredArticles | Blog | FeaturedCategories | FeaturedListings | Features | SocialMediaLink | Footer | Upload | AuditEntry | MessageFlag | Message | Conversation | Group | Review | Transaction | Subscription | PaymentMethod | Address | AuctionLot | UserLocationConsent | RegionalTrends | TrendData | LocationBasedRecommendationSettings | LocationHistory | LocationBasedSearch | LocationBasedListingTargeting | ListingPackage | BidderHistory | Listing | Auction | Bid | Category | User | LocationAlertSettings | UserLocationPreference | Location | SanityImagePaletteSwatch | SanityImagePalette | SanityImageDimensions | SanityImageHotspot | SanityImageCrop | SanityFileAsset | SanityImageAsset | SanityImageMetadata | Geopoint | Slug | SanityAssetSourceData;
export declare const internalGroqTypeReferenceTo: unique symbol;
// Source: sanity/lib/crud/categories/data.ts
// Variable: categoriesQuery
// Query: *[_type == "category" && level == 0][1]{  title,  "categories": subcategories[]->{    _id,    title,    slug {      current    },    "secondLevelSubcategories": subcategories[]->{      _id,      title,      slug {        current      }    },        "thirdLevelSubcategories": subcategories[]->subcategories[]->{      _id,      title,      slug {        current      }    }  },       }
export type CategoriesQueryResult = {
  title: string | null;
  categories: Array<{
    _id: string;
    title: string | null;
    slug: {
      current: string | null;
    } | null;
    secondLevelSubcategories: Array<{
      _id: string;
      title: string | null;
      slug: {
        current: string | null;
      } | null;
    }> | null;
    thirdLevelSubcategories: Array<{
      _id: string;
      title: string | null;
      slug: {
        current: string | null;
      } | null;
    } | null> | null;
  }> | null;
} | null;

// Source: sanity/lib/crud/featuredCategoriesSection/data.ts
// Variable: featuredCategoriesSectionQuery
// Query: *[_type == "featuredCategoriesSection" && isActive == true][0] {    _id,    title,    callToAction,    featuredCategories[]{        _key,        isActive,        sortOrder,        featuredPriority,        overrideTitle,        overrideUrl,        overrideImage,        "category": categoryRef->{        _id,        title,        slug,        path,        "image": image.asset->url,        isActive,        order        },        // computed displayTitle - prefer overrideTitle, then referenced title, else blank        "displayTitle": coalesce(overrideTitle, categoryRef->title),        // computed displayImage - prefer overrideImage, then referenced image        "displayImage": coalesce(overrideImage, categoryRef->image.asset->url)    }  }
export type FeaturedCategoriesSectionQueryResult = {
  _id: string;
  title: string | null;
  callToAction: {
    showViewAllButton?: boolean;
    viewAllButtonText?: string;
    viewAllButtonUrl?: string;
  } | null;
  featuredCategories: null;
} | null;

// Source: sanity/lib/crud/featuredListings/data.ts
// Variable: featuredListingsQuery
// Query: *[_type == "listing" && defined(slug.current) && isFeatured == true]  | order(postedOn desc) [$offset...$limit]{    _id,    user->{      _id,      firstName,      lastName,      fullName,      "profileImage": profileImage.asset->url,      "city": address->city,      "suburb": address->suburb,      "cityAbbr": address->cityAbbreviation,    },    title,    slug,    description,    price,    priceOption,    postedOn,    "images": images[]->{      "alt": image.alt,      "id": image.asset->_id,      "url": image.asset->url,      "width": image.asset->metadata.dimensions.width,      "height": image.asset->metadata.dimensions.height,      "aspectRatio": image.asset->metadata.dimensions.aspectRatio    },  }
export type FeaturedListingsQueryResult = Array<never>;
// Variable: featuredListingsCountQuery
// Query: count(*[_type == "listing" && defined(slug.current) && isFeatured == true])
export type FeaturedListingsCountQueryResult = number;

// Source: sanity/lib/crud/featuredServicesSection/data.ts
// Variable: featuredServicesSectionQuery
// Query: *[_type == "featuredServicesSection"][0] {    _id,    _type,    title,    "services": coalesce(services[]{      callToAction,      description,      "features": coalesce(features[]{        text      }, []),      layout,      "path": callToAction.url,      "serviceTitle": title    }, [])  }
export type FeaturedServicesSectionQueryResult = {
  _id: string;
  _type: "featuredServicesSection";
  title: string | null;
  services: Array<{
    callToAction: {
      text?: string;
      url?: string;
      style?: "ghost" | "outline" | "primary" | "secondary";
      openInNewTab?: boolean;
    } | null;
    description: string | null;
    features: Array<{
      text: string | null;
    }> | Array<never>;
    layout: {
      contentPosition?: "center-content" | "left-content" | "right-content";
      contentWidth?: "40-60" | "50-50" | "60-40";
    } | null;
    path: string | null;
    serviceTitle: string | null;
  }> | Array<never>;
} | null;

// Source: sanity/lib/crud/listing/data.ts
// Variable: listingQuery
// Query: *[_type == "listing" && slug.current == $slug][0]{    _id,    title,    slug,    price,    pricingOption,    description,    site,    postedOn,    expiresAt,    location,    details,    category->{      _id,      title,      slug    },    user->{      _id,      firstName,      lastName,      fullName,      "profileImage": profileImage.asset->url,      "city": address->city,      "suburb": address->suburb,      "cityAbbr": address->cityAbbreviation    },    "images": images[]->{      "alt": image.alt,      "id": image.asset->_id,      "url": image.asset->url,      "width": image.asset->metadata.dimensions.width,      "height": image.asset->metadata.dimensions.height,      "aspectRatio": image.asset->metadata.dimensions.aspectRatio    },  }
export type ListingQueryResult = {
  _id: string;
  title: string | null;
  slug: Slug | null;
  price: number | null;
  pricingOption: "auction" | "contact_for_price" | "fixed_price" | "free" | "negotiable" | null;
  description: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "blockquote" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "normal";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }> | null;
  site: "oly-auto" | "oly-hiring" | "oly-properties" | "oly-services" | "oly" | null;
  postedOn: string | null;
  expiresAt: string | null;
  location: Location | null;
  details: Array<{
    condition?: string;
    accessoriesIncluded?: Array<string>;
    warrantyInformation?: string;
    history?: string;
    customizations?: string;
    maintenanceHistory?: string;
    compatibility?: string;
    originalPackaging?: string;
    usageHistory?: string;
    storage?: string;
    originalPurchaseDate?: string;
    reasonForSelling?: string;
    additionalFeatures?: string;
    serviceRecords?: string;
    userManualAvailability?: string;
    manufacturerSupport?: string;
    compatibilityWithAccessories?: string;
    packagingCondition?: string;
    productHistory?: string;
    transferability?: string;
    petSmokeExposure?: string;
    regulatoryCompliance?: string;
    specialFeatures?: string;
    documentation?: string;
    certification?: string;
    age?: string;
    ownership?: string;
    environmentalImpact?: string;
    knownIssues?: string;
    upgrades?: string;
    _type: "details";
    _key: string;
  }> | null;
  category: {
    _id: string;
    title: string | null;
    slug: Slug | null;
  } | null;
  user: {
    _id: string;
    firstName: string | null;
    lastName: string | null;
    fullName: string | null;
    profileImage: string | null;
    city: null;
    suburb: null;
    cityAbbr: null;
  } | null;
  images: Array<{
    alt: string | null;
    id: string | null;
    url: string | null;
    width: number | null;
    height: number | null;
    aspectRatio: number | null;
  }> | null;
} | null;

// Source: sanity/lib/crud/listingViews/data.ts
// Variable: listingViewsQuery
// Query: *[_type == "listing" && _id == $id][0]{    _id,  views,}
export type ListingViewsQueryResult = {
  _id: string;
  views: null;
} | null;

// Source: sanity/lib/crud/listings/data.ts
// Variable: listingsQuery
// Query: *[_type == "listing" && defined(slug.current)    && ($searchTerm == "" || title match $searchTerm || description match $searchTerm)    && ($locationSearch == "" || user->address->city match $locationSearch                               || user->address->suburb match $locationSearch                               || user->address->cityAbbreviation match $locationSearch)  ] | order(postedOn desc) [$offset...$limit]{    _id,    user->{      _id,      firstName,      lastName,      fullName,      "profileImage": profileImage.asset->url,      "city": address->city,      "suburb": address->suburb,      "cityAbbr": address->cityAbbreviation,    },    title,    slug,    description,    price,    priceOption,    postedOn,    "images": images[]->{      "alt": image.alt,      "id": image.asset->_id,      "url": image.asset->url,      "width": image.asset->metadata.dimensions.width,      "height": image.asset->metadata.dimensions.height,      "aspectRatio": image.asset->metadata.dimensions.aspectRatio    }  }
export type ListingsQueryResult = Array<{
  _id: string;
  user: {
    _id: string;
    firstName: string | null;
    lastName: string | null;
    fullName: string | null;
    profileImage: string | null;
    city: null;
    suburb: null;
    cityAbbr: null;
  } | null;
  title: string | null;
  slug: Slug | null;
  description: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "blockquote" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "normal";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }> | null;
  price: number | null;
  priceOption: null;
  postedOn: string | null;
  images: Array<{
    alt: string | null;
    id: string | null;
    url: string | null;
    width: number | null;
    height: number | null;
    aspectRatio: number | null;
  }> | null;
}>;
// Variable: listingsCountQuery
// Query: count(*[_type == "listing" && defined(slug.current)    && ($searchTerm == "" || title match $searchTerm || description match $searchTerm)    && ($locationSearch == "" || user->address->city match $locationSearch                               || user->address->suburb match $locationSearch                               || user->address->cityAbbreviation match $locationSearch)  ])
export type ListingsCountQueryResult = number;

// Source: sanity/lib/crud/moreFromOly/data.ts
// Variable: moreFromOlyQuery
// Query: *[_type == "moreFromOlySection"][0] {  title,  "sites": coalesce(sites[]{    _id,    _type,    name,    url,    "imageUrl": backgroundImage.asset->url  }, [])}
export type MoreFromOlyQueryResult = {
  title: string | null;
  sites: Array<{
    _id: null;
    _type: "networkSite";
    name: string | null;
    url: string | null;
    imageUrl: string | null;
  }> | Array<never>;
} | null;

// Source: sanity/lib/crud/pages/oly-homepage/data.ts
// Variable: olyHomepageQuery
// Query: *[_type == "olyHomepage" && isActive == true][0] {  _id,  _type,  title,  publishedAt,  isActive,  adSection,  topAdSection,  bottomAdSection,  featuredCategoriesSection,  featuredListingsSection,  featuredServicesSection,  heroSection,  moreFromOlySection,  olyArticlesSection,  sponsoredArticlesSection,}
export type OlyHomepageQueryResult = {
  _id: string;
  _type: "olyHomepage";
  title: string | null;
  publishedAt: string | null;
  isActive: boolean | null;
  adSection: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "adSection";
    };
    sortOrder?: number;
  } | null;
  topAdSection: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "topAdSection";
    };
    sortOrder?: number;
  } | null;
  bottomAdSection: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "bottomAdSection";
    };
    sortOrder?: number;
  } | null;
  featuredCategoriesSection: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "featuredCategoriesSection";
    };
    sortOrder?: number;
  } | null;
  featuredListingsSection: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "featuredListingsSection";
    };
    sortOrder?: number;
  } | null;
  featuredServicesSection: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "featuredServicesSection";
    };
    sortOrder?: number;
  } | null;
  heroSection: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "heroSection";
    };
    sortOrder?: number;
  } | null;
  moreFromOlySection: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "moreFromOlySection";
    };
    sortOrder?: number;
  } | null;
  olyArticlesSection: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "olyArticlesSection";
    };
    sortOrder?: number;
  } | null;
  sponsoredArticlesSection: {
    isEnabled?: boolean;
    sectionTitle?: string;
    reference?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sponsoredArticlesSection";
    };
    sortOrder?: number;
  } | null;
} | null;

// Source: sanity/lib/crud/similarListings/data.ts
// Variable: similarListingsQuery
// Query: *[_type == "listing" &&     defined(slug.current) &&     isActive == true &&     approvedForSale == "approved" &&    _id != $currentListingId &&    (      category._ref == $categoryRef ||      (price >= $minPrice && price <= $maxPrice) ||      user->address->city == $userCity    )  ] | order(postedOn desc) [0...$limit]{    _id,    user->{      _id,      firstName,      lastName,      fullName,      "profileImage": profileImage.asset->url,      "city": address->city,      "suburb": address->suburb,      "cityAbbr": address->cityAbbreviation,    },    title,    slug,    description,    price,    priceOption,    postedOn,    category->{      _id,      title,      slug    },    "images": images[]->{      "alt": image.alt,      "id": image.asset->_id,      "url": image.asset->url,      "width": image.asset->metadata.dimensions.width,      "height": image.asset->metadata.dimensions.height,      "aspectRatio": image.asset->metadata.dimensions.aspectRatio    },  }
export type SimilarListingsQueryResult = Array<{
  _id: string;
  user: {
    _id: string;
    firstName: string | null;
    lastName: string | null;
    fullName: string | null;
    profileImage: string | null;
    city: null;
    suburb: null;
    cityAbbr: null;
  } | null;
  title: string | null;
  slug: Slug | null;
  description: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "blockquote" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "normal";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }> | null;
  price: number | null;
  priceOption: null;
  postedOn: string | null;
  category: {
    _id: string;
    title: string | null;
    slug: Slug | null;
  } | null;
  images: Array<{
    alt: string | null;
    id: string | null;
    url: string | null;
    width: number | null;
    height: number | null;
    aspectRatio: number | null;
  }> | null;
}>;
// Variable: recentListingsQuery
// Query: *[_type == "listing" &&           defined(slug.current) &&           isActive == true &&           approvedForSale == "approved" &&          _id != $currentListingId        ] | order(postedOn desc) [0...$remainingLimit]{          _id,          user->{            _id,            firstName,            lastName,            fullName,            "profileImage": profileImage.asset->url,            "city": address->city,            "suburb": address->suburb,            "cityAbbr": address->cityAbbreviation,          },          title,          slug,          description,          price,          priceOption,          postedOn,          "images": images[]->{            "alt": image.alt,            "id": image.asset->_id,            "url": image.asset->url,            "width": image.asset->metadata.dimensions.width,            "height": image.asset->metadata.dimensions.height,            "aspectRatio": image.asset->metadata.dimensions.aspectRatio          },        }
export type RecentListingsQueryResult = Array<{
  _id: string;
  user: {
    _id: string;
    firstName: string | null;
    lastName: string | null;
    fullName: string | null;
    profileImage: string | null;
    city: null;
    suburb: null;
    cityAbbr: null;
  } | null;
  title: string | null;
  slug: Slug | null;
  description: Array<{
    children?: Array<{
      marks?: Array<string>;
      text?: string;
      _type: "span";
      _key: string;
    }>;
    style?: "blockquote" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "normal";
    listItem?: "bullet" | "number";
    markDefs?: Array<{
      href?: string;
      _type: "link";
      _key: string;
    }>;
    level?: number;
    _type: "block";
    _key: string;
  }> | null;
  price: number | null;
  priceOption: null;
  postedOn: string | null;
  images: Array<{
    alt: string | null;
    id: string | null;
    url: string | null;
    width: number | null;
    height: number | null;
    aspectRatio: number | null;
  }> | null;
}>;

// Source: sanity/lib/crud/user/data.ts
// Variable: userQuery
// Query: *[_type == "user"] {  _id,  email,  firstName,  lastName,  fullName,  "profileImage": profileImage.asset->url}
export type UserQueryResult = Array<{
  _id: string;
  email: null;
  firstName: string | null;
  lastName: string | null;
  fullName: string | null;
  profileImage: string | null;
}>;

// Query TypeMap
import "@sanity/client";
declare module "@sanity/client" {
  interface SanityQueries {
    "*[_type == \"category\" && level == 0][1]{\n  title,\n  \"categories\": subcategories[]->{\n    _id,\n    title,\n    slug {\n      current\n    },\n    \"secondLevelSubcategories\": subcategories[]->{\n      _id,\n      title,\n      slug {\n        current\n      }\n    },\n    \n    \"thirdLevelSubcategories\": subcategories[]->subcategories[]->{\n      _id,\n      title,\n      slug {\n        current\n      }\n    }\n  },\n\n \n    \n  \n}": CategoriesQueryResult;
    "\n  *[_type == \"featuredCategoriesSection\" && isActive == true][0] {\n    _id,\n    title,\n    callToAction,\n    featuredCategories[]{\n        _key,\n        isActive,\n        sortOrder,\n        featuredPriority,\n        overrideTitle,\n        overrideUrl,\n        overrideImage,\n        \"category\": categoryRef->{\n        _id,\n        title,\n        slug,\n        path,\n        \"image\": image.asset->url,\n        isActive,\n        order\n        },\n        // computed displayTitle - prefer overrideTitle, then referenced title, else blank\n        \"displayTitle\": coalesce(overrideTitle, categoryRef->title),\n        // computed displayImage - prefer overrideImage, then referenced image\n        \"displayImage\": coalesce(overrideImage, categoryRef->image.asset->url)\n    }\n  }\n": FeaturedCategoriesSectionQueryResult;
    "\n  *[_type == \"listing\" && defined(slug.current) && isFeatured == true]\n  | order(postedOn desc) [$offset...$limit]{\n    _id,\n    user->{\n      _id,\n      firstName,\n      lastName,\n      fullName,\n      \"profileImage\": profileImage.asset->url,\n      \"city\": address->city,\n      \"suburb\": address->suburb,\n      \"cityAbbr\": address->cityAbbreviation,\n    },\n    title,\n    slug,\n    description,\n    price,\n    priceOption,\n    postedOn,\n    \"images\": images[]->{\n      \"alt\": image.alt,\n      \"id\": image.asset->_id,\n      \"url\": image.asset->url,\n      \"width\": image.asset->metadata.dimensions.width,\n      \"height\": image.asset->metadata.dimensions.height,\n      \"aspectRatio\": image.asset->metadata.dimensions.aspectRatio\n    },\n  }\n": FeaturedListingsQueryResult;
    "\n  count(*[_type == \"listing\" && defined(slug.current) && isFeatured == true])\n": FeaturedListingsCountQueryResult;
    "\n  *[_type == \"featuredServicesSection\"][0] {\n    _id,\n    _type,\n    title,\n    \"services\": coalesce(services[]{\n      callToAction,\n      description,\n      \"features\": coalesce(features[]{\n        text\n      }, []),\n      layout,\n      \"path\": callToAction.url,\n      \"serviceTitle\": title\n    }, [])\n  }\n": FeaturedServicesSectionQueryResult;
    "*[_type == \"listing\" && slug.current == $slug][0]{\n    _id,\n    title,\n    slug,\n    price,\n    pricingOption,\n    description,\n    site,\n    postedOn,\n    expiresAt,\n    location,\n    details,\n    category->{\n      _id,\n      title,\n      slug\n    },\n    user->{\n      _id,\n      firstName,\n      lastName,\n      fullName,\n      \"profileImage\": profileImage.asset->url,\n      \"city\": address->city,\n      \"suburb\": address->suburb,\n      \"cityAbbr\": address->cityAbbreviation\n    },\n    \"images\": images[]->{\n      \"alt\": image.alt,\n      \"id\": image.asset->_id,\n      \"url\": image.asset->url,\n      \"width\": image.asset->metadata.dimensions.width,\n      \"height\": image.asset->metadata.dimensions.height,\n      \"aspectRatio\": image.asset->metadata.dimensions.aspectRatio\n    },\n  }": ListingQueryResult;
    "*[_type == \"listing\" && _id == $id][0]{\n    _id,\n  views,\n}": ListingViewsQueryResult;
    "\n  *[_type == \"listing\" && defined(slug.current)\n    && ($searchTerm == \"\" || title match $searchTerm || description match $searchTerm)\n    && ($locationSearch == \"\" || user->address->city match $locationSearch \n                              || user->address->suburb match $locationSearch \n                              || user->address->cityAbbreviation match $locationSearch)\n  ] | order(postedOn desc) [$offset...$limit]{\n    _id,\n    user->{\n      _id,\n      firstName,\n      lastName,\n      fullName,\n      \"profileImage\": profileImage.asset->url,\n      \"city\": address->city,\n      \"suburb\": address->suburb,\n      \"cityAbbr\": address->cityAbbreviation,\n    },\n    title,\n    slug,\n    description,\n    price,\n    priceOption,\n    postedOn,\n    \"images\": images[]->{\n      \"alt\": image.alt,\n      \"id\": image.asset->_id,\n      \"url\": image.asset->url,\n      \"width\": image.asset->metadata.dimensions.width,\n      \"height\": image.asset->metadata.dimensions.height,\n      \"aspectRatio\": image.asset->metadata.dimensions.aspectRatio\n    }\n  }\n": ListingsQueryResult;
    "\n  count(*[_type == \"listing\" && defined(slug.current)\n    && ($searchTerm == \"\" || title match $searchTerm || description match $searchTerm)\n    && ($locationSearch == \"\" || user->address->city match $locationSearch \n                              || user->address->suburb match $locationSearch \n                              || user->address->cityAbbreviation match $locationSearch)\n  ])\n": ListingsCountQueryResult;
    "*[_type == \"moreFromOlySection\"][0] {\n  title,\n  \"sites\": coalesce(sites[]{\n    _id,\n    _type,\n    name,\n    url,\n    \"imageUrl\": backgroundImage.asset->url\n  }, [])\n}": MoreFromOlyQueryResult;
    "*[_type == \"olyHomepage\" && isActive == true][0] {\n  _id,\n  _type,\n  title,\n  publishedAt,\n  isActive,\n  adSection,\n  topAdSection,\n  bottomAdSection,\n  featuredCategoriesSection,\n  featuredListingsSection,\n  featuredServicesSection,\n  heroSection,\n  moreFromOlySection,\n  olyArticlesSection,\n  sponsoredArticlesSection,\n}": OlyHomepageQueryResult;
    "\n  *[_type == \"listing\" && \n    defined(slug.current) && \n    isActive == true && \n    approvedForSale == \"approved\" &&\n    _id != $currentListingId &&\n    (\n      category._ref == $categoryRef ||\n      (price >= $minPrice && price <= $maxPrice) ||\n      user->address->city == $userCity\n    )\n  ] | order(postedOn desc) [0...$limit]{\n    _id,\n    user->{\n      _id,\n      firstName,\n      lastName,\n      fullName,\n      \"profileImage\": profileImage.asset->url,\n      \"city\": address->city,\n      \"suburb\": address->suburb,\n      \"cityAbbr\": address->cityAbbreviation,\n    },\n    title,\n    slug,\n    description,\n    price,\n    priceOption,\n    postedOn,\n    category->{\n      _id,\n      title,\n      slug\n    },\n    \"images\": images[]->{\n      \"alt\": image.alt,\n      \"id\": image.asset->_id,\n      \"url\": image.asset->url,\n      \"width\": image.asset->metadata.dimensions.width,\n      \"height\": image.asset->metadata.dimensions.height,\n      \"aspectRatio\": image.asset->metadata.dimensions.aspectRatio\n    },\n  }\n": SimilarListingsQueryResult;
    "\n        *[_type == \"listing\" && \n          defined(slug.current) && \n          isActive == true && \n          approvedForSale == \"approved\" &&\n          _id != $currentListingId\n        ] | order(postedOn desc) [0...$remainingLimit]{\n          _id,\n          user->{\n            _id,\n            firstName,\n            lastName,\n            fullName,\n            \"profileImage\": profileImage.asset->url,\n            \"city\": address->city,\n            \"suburb\": address->suburb,\n            \"cityAbbr\": address->cityAbbreviation,\n          },\n          title,\n          slug,\n          description,\n          price,\n          priceOption,\n          postedOn,\n          \"images\": images[]->{\n            \"alt\": image.alt,\n            \"id\": image.asset->_id,\n            \"url\": image.asset->url,\n            \"width\": image.asset->metadata.dimensions.width,\n            \"height\": image.asset->metadata.dimensions.height,\n            \"aspectRatio\": image.asset->metadata.dimensions.aspectRatio\n          },\n        }\n      ": RecentListingsQueryResult;
    "*[_type == \"user\"] {\n  _id,\n  email,\n  firstName,\n  lastName,\n  fullName,\n  \"profileImage\": profileImage.asset->url\n}": UserQueryResult;
  }
}

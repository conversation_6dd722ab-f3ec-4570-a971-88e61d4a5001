@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .main {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: margin-left 0.5s;
    overflow-x: hidden;
    width: 100vw;
    @include only(largeDesktop) {
      // width: rem(1440);
    }

    .nav {
      display: flex;
      justify-content: center;
      width: 100vw;

      @include only(largeDesktop) {
      }
    }
    .modal {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .heroSection {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: rem(96);
      margin-top: rem(24);

      @include only(largeDesktop) {
        margin-bottom: rem(112);
      }
    }
    .title {
      display: flex;
      justify-content: center;
      font-size: $h3 !important;
      font-weight: 600;
      color: $black-four;
      margin-bottom: rem(48);
    }
    .moreFromOly {
      width: 100%;
      margin-top: rem(32);
      margin-bottom: rem(112);
      gap: rem(20);
    }
    .aboveFoldAd {
      width: 100%;
      margin-top: rem(32);
      margin-bottom: rem(256);
    }
    .features {
      width: 95vw;
      margin-bottom: rem(96);

      @include only(largeDesktop) {
        margin-bottom: rem(192);
      }
    }

    .categories {
      margin-bottom: rem(96);

      @include only(largeDesktop) {
        margin-bottom: rem(192);
      }
    }

    .featuredListings {
      margin-bottom: rem(96);

      @include only(largeDesktop) {
        margin-bottom: rem(192);
      }
    }

    .blog {
      margin-bottom: rem(96);

      @include only(largeDesktop) {
        margin-bottom: rem(192);
        width: 100%;
      }
    }

    .belowFoldAd {
      width: 100%;
      margin-top: rem(32);
      margin-bottom: rem(96);
      @include only(largeDesktop) {
        margin-bottom: rem(192);
      }
    }
    .sponsoredArticles {
      margin-bottom: rem(96);

      @include only(largeDesktop) {
        margin-bottom: rem(192);
        width: 100%;
      }
    }
  }
}

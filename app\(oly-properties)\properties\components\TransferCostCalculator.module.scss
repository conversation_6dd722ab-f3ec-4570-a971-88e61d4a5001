.calculator {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
    background-color: #fff;
    border-radius: 10px;
  
    h1 {
      font-size: 2.2rem;
      color: #1a3c5e;
      margin-bottom: 0.5rem;
    }
  
    p {
      font-size: 1.1rem;
      color: #666;
      margin-bottom: 2rem;
    }
  }
  
  .container {
    display: flex;
    gap: 2rem;
  }
  
  .inputSection {
    flex: 1;
  }
  
  .inputGroup {
    margin-bottom: 1.5rem;
  
    label {
      display: block;
      font-size: 1rem;
      color: #333;
      margin-bottom: 0.5rem;
      font-weight: 500;
    }
  
    input,
    select {
      width: 100%;
      padding: 0.8rem;
      border: 1px solid #dcdcdc;
      border-radius: 5px;
      font-size: 1rem;
      outline: none;
      transition: border-color 0.3s;
  
      &:focus {
        border-color: #1a3c5e;
      }
    }
  
    small {
      display: block;
      color: #999;
      font-size: 0.9rem;
      margin-top: 0.3rem;
    }
  }
  
  .inputWithUnit {
    display: flex;
    align-items: center;
  
    input {
      flex: 1;
      border-right: none;
      border-radius: 5px 0 0 5px;
    }
  
    span {
      padding: 0.8rem;
      background-color: #f5f5f5;
      border: 1px solid #dcdcdc;
      border-left: none;
      border-radius: 0 5px 5px 0;
      font-size: 1rem;
      color: #333;
    }
  }
  
  .resultSection {
    flex: 1;
    padding: 1.5rem;
    background-color: #f9f9f9;
    border-radius: 5px;
  
    h3 {
      font-size: 1.2rem;
      color: #1a3c5e;
      margin-bottom: 0.5rem;
    }
  
    p {
      font-size: 1rem;
      color: #666;
      margin-bottom: 1rem;
    }
  
    .resultItem {
      display: flex;
      justify-content: space-between;
      margin-bottom: 1rem;
      font-size: 1.1rem;
      color: #333;
  
      strong {
        color: #1a3c5e;
      }
    }
  
    .disclaimer {
      display: block;
      font-size: 0.9rem;
      color: #999;
      margin-top: 1rem;
    }
  }
  
  .calculateButton {
    display: block;
    width: 100%;
    background-color: #1a3c5e;
    color: white;
    padding: 1rem;
    border: none;
    border-radius: 25px;
    font-size: 1.1rem;
    cursor: pointer;
    margin-top: 1rem;
    transition: background-color 0.3s;
  
    &:hover {
      background-color: #15324b;
    }
  }
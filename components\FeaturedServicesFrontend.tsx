import Image from "@/components/Image";
import styles from "./FeaturedServicesFrontend.module.scss";
import Button from "@/components/Buttons";
import useFeatureInfo from "@/store/featuresInfo";
import useBreakpointStore from "@/store/useBreakpointStore";
import BulletPoint from "@/components/BulletPoint";

type Props = {
  layout: "textLeft" | "textRight";
  path: string;
  image: string;
  title: string;
  description: string;
  cta: string;
  features: {
    id: string;
    feature: string;
  }[];
};

const FeaturedServices = ({
  layout,
  path,
  image,
  title,
  description,
  cta,
  features,
}: Props) => {
  const { isMobile, isTablet } = useBreakpointStore();
  return (
    <>
      {layout === "textLeft" && !isMobile && !isTablet && (
        <TextLeft
          layout={layout}
          path={path}
          image={image}
          title={title}
          description={description}
          cta={cta}
          features={features}
        />
      )}

      {layout === "textRight" && !isMobile && !isTablet && (
        <TextRight
          layout={layout}
          path={path}
          image={image}
          title={title}
          description={description}
          cta={cta}
          features={features}
        />
      )}
      {isTablet && layout === "textLeft" && (
        <TextLeftTablet
          layout={layout}
          path={path}
          image={image}
          title={title}
          description={description}
          cta={cta}
          features={features}
        />
      )}
      {isTablet && layout === "textRight" && (
        <TextRightTablet
          layout={layout}
          path={path}
          image={image}
          title={title}
          description={description}
          cta={cta}
          features={features}
        />
      )}
    </>
  );
};

export default FeaturedServices;

export const TextLeft = ({
  image,
  title,
  description,
  cta,
  features,
}: Props) => {
  const { setIsMoreInfo } = useFeatureInfo();
  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <div className={styles.textContent}>
          <h2 className={styles.title}>{title}</h2>
          <p className={styles.description}>{description}</p>
          <ul className={styles.features}>
            {features.map((feature) => {
              return (
                <li className={styles.feature} key={`textLeft-${feature.id}`}>
                  <div className={styles.bullet}>
                    <BulletPoint />
                  </div>
                  {feature && <p>{feature.feature}</p>}
                </li>
              );
            })}
          </ul>

          <Button
            type="button"
            name="ctaButton"
            buttonType="primary"
            buttonSize="small"
            buttonChildren={cta}
            autoFocus={false}
            className={styles.ctaButton}
            onClick={() => setIsMoreInfo(true)}
          />
        </div>
        <div className={styles.imageContainer}>
          <Image
            src={image}
            alt="Illustration"
            fill
            className={styles.image}
            style={{ objectFit: "cover", borderRadius: "2.5rem" }}
          />
        </div>
      </div>
    </div>
  );
};

export const TextRight = ({
  image,
  title,
  description,
  cta,
  features,
}: Props) => {
  const { setIsMoreInfo } = useFeatureInfo();

  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <div className={styles.imageContainer}>
          <Image
            src={image}
            alt="Illustration"
            fill
            className={styles.image}
            style={{ objectFit: "cover", borderRadius: "2.5rem" }}
          />
        </div>
        <div className={styles.textContent}>
          <h2 className={styles.title}>{title}</h2>
          <p className={styles.description}>{description}</p>
          <div className={styles.features}>
            <ul className={styles.features}>
              {features.map((feature) => {
                return (
                  <li
                    className={styles.feature}
                    key={`textRight-${feature.id}`}
                  >
                    <div className={styles.bullet}>
                      <BulletPoint />
                    </div>
                    {feature && <p>{feature.feature}</p>}
                  </li>
                );
              })}
            </ul>
          </div>

          <Button
            type="button"
            name="ctaButton"
            buttonType="primary"
            buttonSize="small"
            buttonChildren={cta}
            autoFocus={false}
            className={styles.ctaButton}
            onClick={() => setIsMoreInfo(true)}
          />
        </div>
      </div>
    </div>
  );
};

export const TextLeftTablet = ({
  image,
  title,
  description,
  cta,
  features,
}: Props) => {
  const { setIsMoreInfo } = useFeatureInfo();
  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <div className={styles.textContent}>
          <h2 className={styles.title}>{title}</h2>
          <p className={styles.description}>{description}</p>
          <ul className={styles.features}>
            {features.map((feature) => {
              return (
                <li className={styles.feature} key={`textLeftTablet-${feature.id}`}>
                  <div className={styles.bullet}>
                    <BulletPoint />
                  </div>
                  {feature && <p>{feature.feature}</p>}
                </li>
              );
            })}
          </ul>

          <Button
            type="button"
            name="ctaButton"
            buttonType="primary"
            buttonSize="small"
            buttonChildren={cta}
            autoFocus={false}
            className={styles.ctaButton}
            onClick={() => setIsMoreInfo(true)}
          />
        </div>
        <div className={styles.imageContainer}>
          <Image
            src={image}
            alt="Illustration"
            fill
            className={styles.image}
            style={{ objectFit: "cover", borderRadius: "2.5rem" }}
          />
        </div>
      </div>
    </div>
  );
};
export const TextRightTablet = ({
  image,
  title,
  description,
  cta,
  features,
}: Props) => {
  const { setIsMoreInfo } = useFeatureInfo();

  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <div className={styles.imageContainer}>
          <Image
            src={image}
            alt="Illustration"
            fill
            className={styles.image}
            style={{ objectFit: "cover", borderRadius: "3rem" }}
          />
        </div>
        <div className={styles.textContent}>
          <h2 className={styles.title}>{title}</h2>
          <p className={styles.description}>{description}</p>

          <Button
            type="button"
            name="ctaButton"
            buttonType="primary"
            buttonSize="small"
            buttonChildren={cta}
            autoFocus={false}
            className={styles.ctaButton}
            onClick={() => setIsMoreInfo(true)}
          />
        </div>
      </div>
    </div>
  );
};
